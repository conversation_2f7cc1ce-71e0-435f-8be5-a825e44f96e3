package com.pulse.orders.manager.bo;

import com.pulse.orders.persist.dos.OrderLab;
import com.vs.bo.util.BoUtil;
import com.vs.bo.validate.BoValidator;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.hibernate.annotations.DynamicInsert;

import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Transient;

@DynamicInsert
@Getter
@Setter
@Table(name = "order_lab")
@Entity
@AutoGenerated(locked = true, uuid = "27d6071d-5b7d-4a93-8e28-e83b8ba4b2f6|BO|DEFINITION")
public class OrderLabBO implements BoValidator {
    @Transient
    @AutoGenerated(locked = true)
    private Boolean __$transient = true;

    /** 创建时间 */
    @Column(name = "created_at", updatable = false)
    @AutoGenerated(locked = true, uuid = "75b06dc5-8218-527f-8f94-3cd2beec507e")
    private Date createdAt;

    /** 主键 */
    @Column(name = "id")
    @AutoGenerated(locked = true, uuid = "13505fcd-0f07-47fb-a835-ee71de8a4b29")
    @Id
    private String id;

    @Transient
    @AutoGenerated(locked = true)
    private Set<String> loadedProperty = new HashSet<String>();

    @ManyToOne
    @JoinColumn(name = "order_info_id", referencedColumnName = "id")
    @AutoGenerated(locked = true)
    private OrderInfoBO orderInfoBO;

    /** 更新时间 */
    @Column(name = "updated_at")
    @AutoGenerated(locked = true, uuid = "64f2976d-f60e-5eca-9e17-ffd3295178d4")
    private Date updatedAt;

    /** 校验当前BO的数据，在新增和变更的时候回调 */
    @AutoGenerated(locked = true, uuid = "ae1abaf8-c70f-4601-b994-20eb9036cdfa|BO|VALIDATOR")
    @Override
    public void validate() {}

    @AutoGenerated(locked = true)
    public OrderLab convertToOrderLab() {
        OrderLab entity = new OrderLab();
        BoUtil.copyProperties(this, entity, "id", "createdAt", "updatedAt");
        OrderInfoBO orderInfoBO = this.getOrderInfoBO();
        entity.setOrderInfoId(orderInfoBO.getId());
        return entity;
    }

    @AutoGenerated(locked = true)
    public Date getCreatedAt() {
        return this.createdAt;
    }

    @AutoGenerated(locked = true)
    public String getId() {
        return this.id;
    }

    @AutoGenerated(locked = true)
    public OrderInfoBO getOrderInfoBO() {
        return this.orderInfoBO;
    }

    @AutoGenerated(locked = true)
    public String getOrderInfoId() {
        return this.getOrderInfoBO().getId();
    }

    @AutoGenerated(locked = true)
    public Date getUpdatedAt() {
        return this.updatedAt;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setId(String id) {
        this.id = id;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setOrderInfoBO(OrderInfoBO orderInfoBO) {
        this.orderInfoBO = orderInfoBO;
        return (OrderLabBO) this;
    }

    @AutoGenerated(locked = true)
    public OrderLabBO setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
        return (OrderLabBO) this;
    }
}
