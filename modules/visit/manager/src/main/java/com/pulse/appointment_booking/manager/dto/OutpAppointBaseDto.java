package com.pulse.appointment_booking.manager.dto;

import com.pulse.appointment_booking.common.enums.AppointStatusEnum;
import com.pulse.dictionary_basic.common.enums.ClinicVisitTypeEnum;
import com.pulse.dictionary_basic.persist.eo.TimeEo;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "3835da06-0c23-4819-8e4b-c1074b3082c4|DTO|DEFINITION")
public class OutpAppointBaseDto {
    /** 应用ID */
    @AutoGenerated(locked = true, uuid = "90a0ab5e-0e4e-4ad3-ac86-b30eba84d631")
    private String appId;

    /** 预约凭证号 */
    @AutoGenerated(locked = true, uuid = "3db873dc-7014-4428-a78d-b1729b523363")
    private String appointCertificationNumber;

    /** 预约状态 */
    @AutoGenerated(locked = true, uuid = "ded40eaf-de1e-4507-acaf-9ce859efb8a3")
    private AppointStatusEnum appointStatus;

    /** 预约类别ID */
    @AutoGenerated(locked = true, uuid = "3d26e58e-d5e4-4910-a956-b0f65e3824a5")
    private String appointmentCategoryId;

    /** 排班ID */
    @AutoGenerated(locked = true, uuid = "9118faa5-fe49-40c4-a5ba-fa369c693480")
    private String appointmentScheduleId;

    /** 出生日期 */
    @AutoGenerated(locked = true, uuid = "3708c728-6598-4779-ac85-a98c2da826a4")
    private Date birthDate;

    /** 院区id */
    @AutoGenerated(locked = true, uuid = "bc1d9a85-7322-4443-b565-7a5ea2ef9eb6")
    private String campusId;

    /** 取消日期 */
    @AutoGenerated(locked = true, uuid = "d9dd8f68-65f4-4e4f-b58d-ee83ae0a352d")
    private Date cancelDate;

    /** 取消人员ID */
    @AutoGenerated(locked = true, uuid = "8777c658-52a0-4d2a-9aa4-7277635af10a")
    private String cancelOperatorId;

    /** 取消原因 */
    @AutoGenerated(locked = true, uuid = "8b81051c-0560-4cbd-b751-f30acb7ad262")
    private String cancelReason;

    /** 取消预约类型 */
    @AutoGenerated(locked = true, uuid = "ac7969bd-9282-4507-85c3-00ab6679374e")
    private String cancelType;

    /** 手机号 */
    @AutoGenerated(locked = true, uuid = "261b38e6-8239-4783-a5d5-e041d8dc5bef")
    private String cellphone;

    /** 是否收费 */
    @AutoGenerated(locked = true, uuid = "10303a55-4b1a-436e-8c84-a8345136580b")
    private Boolean chargeIs;

    /** 挂号类别ID */
    @AutoGenerated(locked = true, uuid = "5d50017d-545d-4cdc-940f-0f5da043048d")
    private String clinicRegisterTypeId;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "528766cf-af60-4184-bc0b-71049b7a21be")
    private Date createdAt;

    /** 创建人 */
    @AutoGenerated(locked = true, uuid = "f05e4d53-cfe0-4dbc-be03-22972d6fa4d2")
    private String createdBy;

    /** 科室ID */
    @AutoGenerated(locked = true, uuid = "238c7c0b-87ec-4377-ae20-eb274d0c5b5a")
    private String departmentId;

    /** 病案号 */
    @AutoGenerated(locked = true, uuid = "ec2d4ac0-1e85-4e9c-9cd7-8c166bb83b97")
    private String displayId;

    /** 医生ID */
    @AutoGenerated(locked = true, uuid = "ddefc1dc-e2b5-4926-b4b8-c73dd0552efd")
    private String doctorId;

    /** 预约编号 取号时,预约编号+取号密码作为凭证 */
    @AutoGenerated(locked = true, uuid = "e9ce0cd3-d6b8-45a6-988f-2584f29eeffb")
    private String externalRegisterId;

    /** 加号标志 */
    @AutoGenerated(locked = true, uuid = "e9fc9cb6-e01e-4937-a865-81d125017862")
    private Boolean extraRegistrationFlag;

    /** 家庭地址 */
    @AutoGenerated(locked = true, uuid = "ea99d1d2-9588-41b1-b823-1544a27401e0")
    private String homeAddress;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "99881410-d9ff-42cb-a795-a3111dd2b1c3")
    private String id;

    /** 患者预约实名认证证件类型 */
    @AutoGenerated(locked = true, uuid = "5e07e0ab-fba0-46f3-92ae-b3e9434fc63d")
    private String identificationClassId;

    /** 实名认证证件号码 */
    @AutoGenerated(locked = true, uuid = "f7c538e0-b47b-44de-a31e-d3b9223547e0")
    private String identificationNumber;

    /** 互联网收费标志 */
    @AutoGenerated(locked = true, uuid = "f5b1fc4d-51a8-4d91-a6a1-785257621e0e")
    private Boolean internetPaymentFlag;

    /** 乐观锁字段 乐观锁字段 */
    @AutoGenerated(locked = true, uuid = "b094e470-5030-4c90-ad49-70d70d8266a9")
    private Long lockVersion;

    /** MDT诊疗费类别 */
    @AutoGenerated(locked = true, uuid = "0319e0fa-4486-43c4-9458-0e21ab5bccc7")
    private String mdtFeeType;

    /** 预约日期 */
    @AutoGenerated(locked = true, uuid = "e45bcca2-8b09-450a-8a3a-331e95d0bb8a")
    private Date operateDate;

    /** 预约人员ID */
    @AutoGenerated(locked = true, uuid = "dd089b08-0fba-4b73-b3aa-684ef4049611")
    private String operatorId;

    /** 门诊住院标志 */
    @AutoGenerated(locked = true, uuid = "c2e87f03-5ae2-4301-bbe2-7b1c4a9b3e2b")
    private ClinicVisitTypeEnum outpInpType;

    /** 患者ID */
    @AutoGenerated(locked = true, uuid = "0057aec9-bb07-43ed-8b24-1113cd24da06")
    private String patientId;

    /** 患者名称 未建档患者使用 */
    @AutoGenerated(locked = true, uuid = "a9e5c782-373a-4a22-a141-0783d5316e36")
    private String patientName;

    /** 省预约平台上传标志 */
    @AutoGenerated(locked = true, uuid = "cba1d512-abf6-44e3-9a70-a626effaa6e0")
    private Boolean provinceUploadFlag;

    /** 代理挂号标志 */
    @AutoGenerated(locked = true, uuid = "66388730-d7bf-4d6d-a1a6-1b8893f9ee20")
    private Boolean proxyRegistrationFlag;

    /** 提醒手机 */
    @AutoGenerated(locked = true, uuid = "65b2f68b-7d5d-4660-8468-98508afdd7b6")
    private String reminderMobile;

    /** 号源序号 */
    @AutoGenerated(locked = true, uuid = "e9dbe6fc-6267-4950-aa82-53703efaa7a5")
    private Long serialNumber;

    /** 来源ID */
    @AutoGenerated(locked = true, uuid = "40480be4-a573-4004-b824-77c1f6bc94ea")
    private String sourceId;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "c89257cb-107b-45ba-80a6-b1ca01b38b67")
    private Date updatedAt;

    /** 更新人 */
    @AutoGenerated(locked = true, uuid = "56592eb0-eee9-4ce5-83a3-7f6dce6b1ac9")
    private String updatedBy;

    /** 就诊卡ID */
    @AutoGenerated(locked = true, uuid = "c28e2d8e-4a63-440f-85ac-de7ab9a831e5")
    private String visitCardId;

    /** 就诊日期 */
    @AutoGenerated(locked = true, uuid = "a969d854-22bd-4d92-a859-d277c6a4f3a4")
    private Date visitDate;

    /** 就诊科室代码 */
    @AutoGenerated(locked = true, uuid = "b785e8a7-6e41-44eb-8799-9f14321c9117")
    private String visitDepartmentId;

    /** 候诊结束时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "c75ccd94-6afa-4159-bb86-adbf2ee072d9")
    private TimeEo waitingEndTime;

    /** 候诊开始时间 */
    @Valid
    @AutoGenerated(locked = true, uuid = "cc146132-c109-4bb4-a6b4-0bf1cc91fa36")
    private TimeEo waitingStartTime;
}
