package com.pulse.drug_circulation.service.converter;

import com.pulse.drug_circulation.manager.dto.DrugBorrowDetailDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

@Component
@AutoGenerated(locked = false, uuid = "13570340-c8bc-39e6-a94d-5054113ce8e9")
public class DrugBorrowDetailDtoServiceConverter {

    @AutoGenerated(locked = false)
    public List<DrugBorrowDetailDto> DrugBorrowDetailDtoConverter(
            List<DrugBorrowDetailDto> drugBorrowDetailDtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        return drugBorrowDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
