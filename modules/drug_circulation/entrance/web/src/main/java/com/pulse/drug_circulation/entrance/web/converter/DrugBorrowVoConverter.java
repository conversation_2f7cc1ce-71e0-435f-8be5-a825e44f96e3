package com.pulse.drug_circulation.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_circulation.entrance.web.query.assembler.DrugBorrowVoDataAssembler;
import com.pulse.drug_circulation.entrance.web.query.assembler.DrugBorrowVoDataAssembler.DrugBorrowVoDataHolder;
import com.pulse.drug_circulation.entrance.web.query.collector.DrugBorrowVoDataCollector;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowDetailBaseVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowVo.StaffBaseVo;
import com.pulse.drug_circulation.manager.dto.DrugBorrowDetailBaseDto;
import com.pulse.drug_circulation.manager.dto.DrugBorrowDto;
import com.pulse.drug_circulation.service.DrugBorrowBaseDtoService;
import com.pulse.organization.manager.dto.StaffBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugBorrowVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "09b72806-a240-4212-b243-89ac1d582fdd|VO|CONVERTER")
public class DrugBorrowVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowBaseDtoService drugBorrowBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowDetailBaseVoConverter drugBorrowDetailBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowVoDataAssembler drugBorrowVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowVoDataCollector drugBorrowVoDataCollector;

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = true, uuid = "03bea5fa-2653-3c14-a5ce-9a3a9690d2bb")
    public DrugBorrowVo.StaffBaseVo convertToStaffBaseVo(StaffBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToStaffBaseVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugBorrowDto转换成DrugBorrowVo */
    @AutoGenerated(locked = false, uuid = "09b72806-a240-4212-b243-89ac1d582fdd-converter-Map")
    public Map<DrugBorrowDto, DrugBorrowVo> convertToDrugBorrowVoMap(List<DrugBorrowDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<StaffBaseDto, StaffBaseVo> applyStaffMap =
                convertToStaffBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(DrugBorrowDto::getApplyStaff)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<StaffBaseDto, StaffBaseVo> borrowStaffMap =
                convertToStaffBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(DrugBorrowDto::getBorrowStaff)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<DrugBorrowDetailBaseDto, DrugBorrowDetailBaseVo> drugBorrowDetailListMap =
                drugBorrowDetailBaseVoConverter.convertToDrugBorrowDetailBaseVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getDrugBorrowDetailList()))
                                .flatMap(dto -> dto.getDrugBorrowDetailList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<DrugBorrowDto, DrugBorrowVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugBorrowVo vo = new DrugBorrowVo();
                                            vo.setId(dto.getId());
                                            vo.setApplyStaff(
                                                    dto.getApplyStaff() == null
                                                            ? null
                                                            : applyStaffMap.get(
                                                                    dto.getApplyStaff()));
                                            vo.setBorrowId(dto.getBorrowId());
                                            vo.setBorrowStaff(
                                                    dto.getBorrowStaff() == null
                                                            ? null
                                                            : borrowStaffMap.get(
                                                                    dto.getBorrowStaff()));
                                            vo.setStorageCode(dto.getStorageCode());
                                            vo.setBorrowDepartment(dto.getBorrowDepartment());
                                            vo.setBorrowType(dto.getBorrowType());
                                            vo.setCost(dto.getCost());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setInventoryNumber(dto.getInventoryNumber());
                                            vo.setPlanReturnDateTime(dto.getPlanReturnDateTime());
                                            vo.setReturnNumber(dto.getReturnNumber());
                                            vo.setReturnStatus(dto.getReturnStatus());
                                            vo.setSubmitDateTime(dto.getSubmitDateTime());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setDrugBorrowDetailList(
                                                    dto.getDrugBorrowDetailList() == null
                                                            ? null
                                                            : dto.getDrugBorrowDetailList().stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    drugBorrowDetailListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugBorrowDto转换成DrugBorrowVo */
    @AutoGenerated(locked = true, uuid = "09b72806-a240-4212-b243-89ac1d582fdd-converter-list")
    public List<DrugBorrowVo> convertToDrugBorrowVoList(List<DrugBorrowDto> dtoList) {
        return new ArrayList<>(convertToDrugBorrowVoMap(dtoList).values());
    }

    /** 使用默认方式组装DrugBorrowVo列表数据 */
    @AutoGenerated(locked = true, uuid = "13c3c3d5-6c69-3b0f-8a37-97829deecb27")
    public List<DrugBorrowVo> convertAndAssembleDataList(List<DrugBorrowDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        DrugBorrowVoDataHolder dataHolder = new DrugBorrowVoDataHolder();
        dataHolder.setRootBaseDtoList(
                drugBorrowBaseDtoService.getByIds(
                        dtoList.stream().map(DrugBorrowDto::getId).collect(Collectors.toList())));
        Map<String, DrugBorrowVo> voMap =
                convertToDrugBorrowVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugBorrowVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        drugBorrowVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = false, uuid = "360bb936-95e8-4963-8149-08a1029772d0-converter-Map")
    public Map<StaffBaseDto, DrugBorrowVo.StaffBaseVo> convertToStaffBaseVoMap(
            List<StaffBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<StaffBaseDto, StaffBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            StaffBaseVo vo = new StaffBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setGender(dto.getGender());
                                            vo.setInputCode(dto.getInputCode());
                                            vo.setName(dto.getName());
                                            vo.setStaffNumber(dto.getStaffNumber());
                                            vo.setPhoneNumber(dto.getPhoneNumber());
                                            vo.setShortPhoneNumber(dto.getShortPhoneNumber());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把StaffBaseDto转换成StaffBaseVo */
    @AutoGenerated(locked = true, uuid = "360bb936-95e8-4963-8149-08a1029772d0-converter-list")
    public List<DrugBorrowVo.StaffBaseVo> convertToStaffBaseVoList(List<StaffBaseDto> dtoList) {
        return new ArrayList<>(convertToStaffBaseVoMap(dtoList).values());
    }

    /** 把DrugBorrowDto转换成DrugBorrowVo */
    @AutoGenerated(locked = true, uuid = "932019cf-ba8a-341b-ab90-abfaa866cfd1")
    public DrugBorrowVo convertToDrugBorrowVo(DrugBorrowDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugBorrowVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装DrugBorrowVo数据 */
    @AutoGenerated(locked = true, uuid = "fb868217-e7c6-30e3-b26d-16da5a847c34")
    public DrugBorrowVo convertAndAssembleData(DrugBorrowDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }
}
