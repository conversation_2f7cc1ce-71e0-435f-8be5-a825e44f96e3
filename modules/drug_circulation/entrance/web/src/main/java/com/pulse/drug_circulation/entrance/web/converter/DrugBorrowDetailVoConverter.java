package com.pulse.drug_circulation.entrance.web.converter;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.drug_circulation.entrance.web.query.assembler.DrugBorrowDetailVoDataAssembler;
import com.pulse.drug_circulation.entrance.web.query.assembler.DrugBorrowDetailVoDataAssembler.DrugBorrowDetailVoDataHolder;
import com.pulse.drug_circulation.entrance.web.query.collector.DrugBorrowDetailVoDataCollector;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowBaseVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowDetailBaseVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowDetailVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowDetailVo.DrugOriginBatchInventoryBaseVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowDetailVo.DrugOriginSpecificationWithDrugOriginVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugOriginBaseVo;
import com.pulse.drug_circulation.manager.dto.DrugBorrowBaseDto;
import com.pulse.drug_circulation.manager.dto.DrugBorrowDetailBaseDto;
import com.pulse.drug_circulation.manager.dto.DrugBorrowDetailDto;
import com.pulse.drug_circulation.service.DrugBorrowDetailBaseDtoService;
import com.pulse.drug_dictionary.manager.dto.DrugOriginBaseDto;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationWithDrugOriginDto;
import com.pulse.drug_inventory.manager.dto.DrugOriginBatchInventoryBaseDto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 完成Dto到DrugBorrowDetailVo的转换 */
@Component
@AutoGenerated(locked = false, uuid = "7fc09634-5c78-42e1-a910-5fdff277f005|VO|CONVERTER")
public class DrugBorrowDetailVoConverter {
    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowBaseVoConverter drugBorrowBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowDetailBaseDtoService drugBorrowDetailBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowDetailBaseVoConverter drugBorrowDetailBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowDetailVoDataAssembler drugBorrowDetailVoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowDetailVoDataCollector drugBorrowDetailVoDataCollector;

    @AutoGenerated(locked = true)
    @Resource
    private DrugOriginBaseVoConverter drugOriginBaseVoConverter;

    /** 把DrugBorrowDetailDto转换成DrugBorrowDetailVo */
    @AutoGenerated(locked = true, uuid = "5a65e7c1-d86a-3d9a-9a51-a1392fdf93af")
    public DrugBorrowDetailVo convertToDrugBorrowDetailVo(DrugBorrowDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugBorrowDetailVoList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 把DrugOriginBatchInventoryBaseDto转换成DrugOriginBatchInventoryBaseVo */
    @AutoGenerated(locked = true, uuid = "6758f6c7-2920-3ff9-957c-83519f80139b")
    public DrugBorrowDetailVo.DrugOriginBatchInventoryBaseVo
            convertToDrugOriginBatchInventoryBaseVo(DrugOriginBatchInventoryBaseDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugOriginBatchInventoryBaseVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把DrugBorrowDetailDto转换成DrugBorrowDetailVo */
    @AutoGenerated(locked = false, uuid = "7fc09634-5c78-42e1-a910-5fdff277f005-converter-Map")
    public Map<DrugBorrowDetailDto, DrugBorrowDetailVo> convertToDrugBorrowDetailVoMap(
            List<DrugBorrowDetailDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<DrugBorrowDetailBaseDto, DrugBorrowDetailBaseVo> drugBorrowDetailMap =
                drugBorrowDetailBaseVoConverter.convertToDrugBorrowDetailBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(DrugBorrowDetailDto::getDrugBorrowDetail)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<DrugBorrowBaseDto, DrugBorrowBaseVo> drugBorrowMap =
                drugBorrowBaseVoConverter.convertToDrugBorrowBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(DrugBorrowDetailDto::getDrugBorrow)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<DrugOriginSpecificationWithDrugOriginDto, DrugOriginSpecificationWithDrugOriginVo>
                drugOriginSpecificationMap =
                        convertToDrugOriginSpecificationWithDrugOriginVoMap(
                                dtoList.stream()
                                        .filter(Objects::nonNull)
                                        .map(DrugBorrowDetailDto::getDrugOriginSpecification)
                                        .filter(Objects::nonNull)
                                        .collect(Collectors.toList()));
        Map<DrugOriginBatchInventoryBaseDto, DrugOriginBatchInventoryBaseVo> batchInventoryMap =
                convertToDrugOriginBatchInventoryBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(DrugBorrowDetailDto::getBatchInventory)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<DrugBorrowDetailBaseDto, DrugBorrowDetailBaseVo> drugBorrowDetailListMap =
                drugBorrowDetailBaseVoConverter.convertToDrugBorrowDetailBaseVoMap(
                        dtoList.stream()
                                .filter(
                                        dto ->
                                                CollectionUtil.isNotEmpty(
                                                        dto.getDrugBorrowDetailList()))
                                .flatMap(dto -> dto.getDrugBorrowDetailList().stream())
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<DrugBorrowDetailDto, DrugBorrowDetailVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugBorrowDetailVo vo = new DrugBorrowDetailVo();
                                            vo.setId(dto.getId());
                                            vo.setDrugBorrowDetail(
                                                    dto.getDrugBorrowDetail() == null
                                                            ? null
                                                            : drugBorrowDetailMap.get(
                                                                    dto.getDrugBorrowDetail()));
                                            vo.setDrugBorrow(
                                                    dto.getDrugBorrow() == null
                                                            ? null
                                                            : drugBorrowMap.get(
                                                                    dto.getDrugBorrow()));
                                            vo.setDrugOriginSpecification(
                                                    dto.getDrugOriginSpecification() == null
                                                            ? null
                                                            : drugOriginSpecificationMap.get(
                                                                    dto
                                                                            .getDrugOriginSpecification()));
                                            vo.setAmount(dto.getAmount());
                                            vo.setBatchInventory(
                                                    dto.getBatchInventory() == null
                                                            ? null
                                                            : batchInventoryMap.get(
                                                                    dto.getBatchInventory()));
                                            vo.setCost(dto.getCost());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setPrice(dto.getPrice());
                                            vo.setReturnStatus(dto.getReturnStatus());
                                            vo.setReturnedAmount(dto.getReturnedAmount());
                                            vo.setSortNumber(dto.getSortNumber());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setDrugName(dto.getDrugName());
                                            vo.setPurchasePrice(dto.getPurchasePrice());
                                            vo.setStock(dto.getStock());
                                            vo.setUnit(dto.getUnit());
                                            vo.setDrugBorrowDetailList(
                                                    dto.getDrugBorrowDetailList() == null
                                                            ? null
                                                            : dto.getDrugBorrowDetailList().stream()
                                                                    .map(
                                                                            tmp ->
                                                                                    drugBorrowDetailListMap
                                                                                            .get(
                                                                                                    tmp))
                                                                    .collect(Collectors.toList()));
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugBorrowDetailDto转换成DrugBorrowDetailVo */
    @AutoGenerated(locked = true, uuid = "7fc09634-5c78-42e1-a910-5fdff277f005-converter-list")
    public List<DrugBorrowDetailVo> convertToDrugBorrowDetailVoList(
            List<DrugBorrowDetailDto> dtoList) {
        return new ArrayList<>(convertToDrugBorrowDetailVoMap(dtoList).values());
    }

    /** 把DrugOriginSpecificationWithDrugOriginDto转换成DrugOriginSpecificationWithDrugOriginVo */
    @AutoGenerated(locked = true, uuid = "8797f7b3-bbc5-3853-a118-12f2c205ef2f")
    public DrugBorrowDetailVo.DrugOriginSpecificationWithDrugOriginVo
            convertToDrugOriginSpecificationWithDrugOriginVo(
                    DrugOriginSpecificationWithDrugOriginDto dto) {
        if (dto == null) {
            return null;
        }
        return convertToDrugOriginSpecificationWithDrugOriginVoList(List.of(dto)).stream()
                .findAny()
                .orElse(null);
    }

    /** 把DrugOriginSpecificationWithDrugOriginDto转换成DrugOriginSpecificationWithDrugOriginVo */
    @AutoGenerated(locked = false, uuid = "8e3bfb58-5513-4572-8c49-7e5353ed340d-converter-Map")
    public Map<
                    DrugOriginSpecificationWithDrugOriginDto,
                    DrugBorrowDetailVo.DrugOriginSpecificationWithDrugOriginVo>
            convertToDrugOriginSpecificationWithDrugOriginVoMap(
                    List<DrugOriginSpecificationWithDrugOriginDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }
        Map<DrugOriginBaseDto, DrugOriginBaseVo> drugOriginMap =
                drugOriginBaseVoConverter.convertToDrugOriginBaseVoMap(
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .map(DrugOriginSpecificationWithDrugOriginDto::getDrugOrigin)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
        Map<DrugOriginSpecificationWithDrugOriginDto, DrugOriginSpecificationWithDrugOriginVo>
                voMap =
                        dtoList.stream()
                                .filter(Objects::nonNull)
                                .collect(
                                        Collectors.toMap(
                                                Function.identity(),
                                                dto -> {
                                                    DrugOriginSpecificationWithDrugOriginVo vo =
                                                            new DrugOriginSpecificationWithDrugOriginVo();
                                                    vo.setId(dto.getId());
                                                    vo.setDrugSpecificationDetailId(
                                                            dto.getDrugSpecificationDetailId());
                                                    vo.setDrugOrigin(
                                                            dto.getDrugOrigin() == null
                                                                    ? null
                                                                    : drugOriginMap.get(
                                                                            dto.getDrugOrigin()));
                                                    vo.setPriceItemCode(dto.getPriceItemCode());
                                                    vo.setSpecificationType(
                                                            dto.getSpecificationType());
                                                    vo.setDrugSpecification(
                                                            dto.getDrugSpecification());
                                                    vo.setUnit(dto.getUnit());
                                                    vo.setAmountPerPackage(
                                                            dto.getAmountPerPackage());
                                                    vo.setDefaultUsedFlag(dto.getDefaultUsedFlag());
                                                    vo.setCreatedAt(dto.getCreatedAt());
                                                    vo.setUpdatedAt(dto.getUpdatedAt());
                                                    vo.setBidPurchasePrice(
                                                            dto.getBidPurchasePrice());
                                                    vo.setReferenceRetailPriceOne(
                                                            dto.getReferenceRetailPriceOne());
                                                    vo.setReferenceRetailPriceTow(
                                                            dto.getReferenceRetailPriceTow());
                                                    vo.setReferenceRetailPriceThree(
                                                            dto.getReferenceRetailPriceThree());
                                                    vo.setReferenceRetailPriceFour(
                                                            dto.getReferenceRetailPriceFour());
                                                    vo.setReferenceRetailPriceFive(
                                                            dto.getReferenceRetailPriceFive());
                                                    vo.setInsurancePayPrice(
                                                            dto.getInsurancePayPrice());
                                                    return vo;
                                                },
                                                (o1, o2) -> o1,
                                                LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugOriginSpecificationWithDrugOriginDto转换成DrugOriginSpecificationWithDrugOriginVo */
    @AutoGenerated(locked = true, uuid = "8e3bfb58-5513-4572-8c49-7e5353ed340d-converter-list")
    public List<DrugBorrowDetailVo.DrugOriginSpecificationWithDrugOriginVo>
            convertToDrugOriginSpecificationWithDrugOriginVoList(
                    List<DrugOriginSpecificationWithDrugOriginDto> dtoList) {
        return new ArrayList<>(
                convertToDrugOriginSpecificationWithDrugOriginVoMap(dtoList).values());
    }

    /** 把DrugOriginBatchInventoryBaseDto转换成DrugOriginBatchInventoryBaseVo */
    @AutoGenerated(locked = false, uuid = "aec60616-e37c-4b96-8897-ade2153b21c1-converter-Map")
    public Map<DrugOriginBatchInventoryBaseDto, DrugBorrowDetailVo.DrugOriginBatchInventoryBaseVo>
            convertToDrugOriginBatchInventoryBaseVoMap(
                    List<DrugOriginBatchInventoryBaseDto> dtoList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(dtoList)) {
            return new HashMap<>();
        }

        Map<DrugOriginBatchInventoryBaseDto, DrugOriginBatchInventoryBaseVo> voMap =
                dtoList.stream()
                        .filter(Objects::nonNull)
                        .collect(
                                Collectors.toMap(
                                        Function.identity(),
                                        dto -> {
                                            DrugOriginBatchInventoryBaseVo vo =
                                                    new DrugOriginBatchInventoryBaseVo();
                                            vo.setId(dto.getId());
                                            vo.setBatchId(dto.getBatchId());
                                            vo.setDrugOriginSpecificationId(
                                                    dto.getDrugOriginSpecificationId());
                                            vo.setInventoryId(dto.getInventoryId());
                                            vo.setDrugOriginCode(dto.getDrugOriginCode());
                                            vo.setStorageCode(dto.getStorageCode());
                                            vo.setSupplyId(dto.getSupplyId());
                                            vo.setAmount(dto.getAmount());
                                            vo.setBatchNumber(dto.getBatchNumber());
                                            vo.setCreatedAt(dto.getCreatedAt());
                                            vo.setExpirationDate(dto.getExpirationDate());
                                            vo.setGcpCode(dto.getGcpCode());
                                            vo.setUpdatedAt(dto.getUpdatedAt());
                                            vo.setVirtualAmount(dto.getVirtualAmount());
                                            vo.setPurchasePrice(dto.getPurchasePrice());
                                            vo.setRetailPrice(dto.getRetailPrice());
                                            vo.setImportDateTime(dto.getImportDateTime());
                                            return vo;
                                        },
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义处理逻辑
        /** This block is generated by vs, do not modify, start anchor 2 */
        return voMap;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 把DrugOriginBatchInventoryBaseDto转换成DrugOriginBatchInventoryBaseVo */
    @AutoGenerated(locked = true, uuid = "aec60616-e37c-4b96-8897-ade2153b21c1-converter-list")
    public List<DrugBorrowDetailVo.DrugOriginBatchInventoryBaseVo>
            convertToDrugOriginBatchInventoryBaseVoList(
                    List<DrugOriginBatchInventoryBaseDto> dtoList) {
        return new ArrayList<>(convertToDrugOriginBatchInventoryBaseVoMap(dtoList).values());
    }

    /** 使用默认方式组装DrugBorrowDetailVo数据 */
    @AutoGenerated(locked = true, uuid = "b209fcdc-c799-3fe4-b0d3-4bbf49d094bf")
    public DrugBorrowDetailVo convertAndAssembleData(DrugBorrowDetailDto dto) {
        if (dto == null) {
            return null;
        }
        return convertAndAssembleDataList(List.of(dto)).stream().findAny().orElse(null);
    }

    /** 使用默认方式组装DrugBorrowDetailVo列表数据 */
    @AutoGenerated(locked = true, uuid = "d6f23cc1-c8ff-344f-bea5-2936c014fa11")
    public List<DrugBorrowDetailVo> convertAndAssembleDataList(List<DrugBorrowDetailDto> dtoList) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        DrugBorrowDetailVoDataHolder dataHolder = new DrugBorrowDetailVoDataHolder();
        dataHolder.setRootBaseDtoList(
                drugBorrowDetailBaseDtoService.getByIds(
                        dtoList.stream()
                                .map(DrugBorrowDetailDto::getId)
                                .collect(Collectors.toList())));
        Map<String, DrugBorrowDetailVo> voMap =
                convertToDrugBorrowDetailVoMap(dtoList).entrySet().stream()
                        .collect(
                                Collectors.toMap(
                                        entry -> entry.getKey().getId(),
                                        entry -> entry.getValue(),
                                        (o1, o2) -> o1,
                                        LinkedHashMap::new));
        drugBorrowDetailVoDataCollector.collectDataWithDtoData(dtoList, dataHolder);
        drugBorrowDetailVoDataAssembler.assembleData(voMap, dataHolder);
        return dtoList.stream()
                .map(dto -> voMap.get(dto.getId()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
