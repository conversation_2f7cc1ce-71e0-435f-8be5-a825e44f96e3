package com.pulse.drug_circulation.entrance.web.vo;

import com.pulse.drug_circulation.common.enums.ReturnStatusEnum;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowDetailVo.DrugOriginBatchInventoryBaseVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowDetailVo.DrugOriginSpecificationWithDrugOriginVo;
import com.pulse.drug_dictionary.common.enums.SpecificationTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "7fc09634-5c78-42e1-a910-5fdff277f005|VO|DEFINITION")
public class DrugBorrowDetailVo {
    /** 数量 */
    @AutoGenerated(locked = true, uuid = "b57da249-cb88-49ac-95fb-14798f58ff45")
    private Long amount;

    /** 库存id */
    @Valid
    @AutoGenerated(locked = true, uuid = "88708405-e17d-45cb-a8d0-5b7b9a0559ba")
    private DrugOriginBatchInventoryBaseVo batchInventory;

    /** 金额 */
    @AutoGenerated(locked = true, uuid = "1b524ad7-9fd1-4e6d-ba7c-348af28c4bb1")
    private BigDecimal cost;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "aaf96f49-207d-483c-8e4f-c64c6435ee9d")
    private Date createdAt;

    /** 借还药id */
    @Valid
    @AutoGenerated(locked = true, uuid = "1985c68e-b424-4800-a771-81ffacc75163")
    private DrugBorrowBaseVo drugBorrow;

    /** 还药对应借药明细id */
    @Valid
    @AutoGenerated(locked = true, uuid = "eeda0718-**************-1ce85c178e56")
    private DrugBorrowDetailBaseVo drugBorrowDetail;

    /** 还药明细 */
    @Valid
    @AutoGenerated(locked = true, uuid = "bdf8421b-88be-4eb1-86cf-7c54c6986c5e")
    private List<DrugBorrowDetailBaseVo> drugBorrowDetailList;

    /** 药物名称 */
    @AutoGenerated(locked = true, uuid = "9903e5fd-fe36-433a-a764-79536c7e54b2")
    private String drugName;

    /** 药品产地规格id */
    @Valid
    @AutoGenerated(locked = true, uuid = "a0afd08a-f291-4f2e-bb5e-b8dc6d9d46f9")
    private DrugOriginSpecificationWithDrugOriginVo drugOriginSpecification;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "bbb1b9e6-e34c-475c-a70e-c748e895aced")
    private String id;

    /** 单价 */
    @AutoGenerated(locked = true, uuid = "7328944a-f8da-4d63-819d-463ac9d57e07")
    private BigDecimal price;

    /** 进价 */
    @AutoGenerated(locked = true, uuid = "ffb21e8e-cd69-4b10-870e-70eebe691627")
    private BigDecimal purchasePrice;

    /** 还药状态 未还、部分还、全还 */
    @AutoGenerated(locked = true, uuid = "037052c4-71ff-455a-97e6-1e574a31b0ea")
    private ReturnStatusEnum returnStatus;

    /** 已还药数量 */
    @AutoGenerated(locked = true, uuid = "a1c4ff4e-80c5-4b45-a781-503aad4a2efc")
    private Long returnedAmount;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "6fb87570-300a-4667-b766-fdce9428cec9")
    private Long sortNumber;

    /** 库存数量 */
    @AutoGenerated(locked = true, uuid = "e43c3b75-89d5-4156-8d76-a1baf9cf1358")
    private BigDecimal stock;

    /** 单位 */
    @AutoGenerated(locked = true, uuid = "aff42eff-f818-4d9c-8930-d94464e11455")
    private String unit;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "ac990127-d4f9-4229-b10a-b59ab3fb96d7")
    private Date updatedAt;

    @Setter
    @Getter
    public static class DrugOriginSpecificationWithDrugOriginVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "917b5583-716f-466a-91d4-a116fcd18332")
        private String id;

        /** 药品规格明细id */
        @AutoGenerated(locked = true, uuid = "57e9dc7b-86d2-4736-9bea-bb9fa8306e9b")
        private String drugSpecificationDetailId;

        /** 药品产地 */
        @Valid
        @AutoGenerated(locked = true, uuid = "cde1f346-ece5-4f76-9174-59af61233a09")
        private DrugOriginBaseVo drugOrigin;

        /** 价表项目编码 价表唯一编码（非id） */
        @AutoGenerated(locked = true, uuid = "f25f3f85-c518-4478-b9c6-850e5a476bdc")
        private String priceItemCode;

        /** 规格类型 包装规格、最小规格、其他拆分规格 */
        @AutoGenerated(locked = true, uuid = "7d9c920c-8afd-40ed-bdcb-23995cd1a067")
        private SpecificationTypeEnum specificationType;

        /** 规格 */
        @AutoGenerated(locked = true, uuid = "b186397c-a755-4e61-ad5c-eb7dc5798a9e")
        private String drugSpecification;

        /** 单位 */
        @AutoGenerated(locked = true, uuid = "1116711e-65be-4fd3-86a0-c9160780ff19")
        private String unit;

        /** 拆分系数 当前规格可以拆分出多少个最小规格 */
        @AutoGenerated(locked = true, uuid = "8181136a-7d9a-4fc4-a7bf-1f1224b448d0")
        private Long amountPerPackage;

        /** 是否标准规格 药库/药房默认使用规格 */
        @AutoGenerated(locked = true, uuid = "51b9406f-5c57-450d-b750-1ab1a6ec60c1")
        private Boolean defaultUsedFlag;

        /** 创建时间 */
        @AutoGenerated(locked = true, uuid = "26ca9dfd-bf68-4e01-b9e8-fce952968703")
        private Date createdAt;

        /** 更新时间 */
        @AutoGenerated(locked = true, uuid = "c9e9cc1a-ba6d-4dc1-8186-f4bb35989746")
        private Date updatedAt;

        /** 招标进价 */
        @AutoGenerated(locked = true, uuid = "9c5eb7d5-02fc-4e21-8c7c-965064d497b0")
        private BigDecimal bidPurchasePrice;

        /** 参考零售价一 */
        @AutoGenerated(locked = true, uuid = "0a5f7141-0a04-40b9-b5b2-556b400cdf2a")
        private BigDecimal referenceRetailPriceOne;

        /** 参考零售价二 */
        @AutoGenerated(locked = true, uuid = "2cd55e98-22c5-4ea3-9a87-8ddcab5bc790")
        private BigDecimal referenceRetailPriceTow;

        /** 参考零售价三 */
        @AutoGenerated(locked = true, uuid = "281d8520-e142-4b84-aad9-40523ff2ca9c")
        private BigDecimal referenceRetailPriceThree;

        /** 参考零售价四 */
        @AutoGenerated(locked = true, uuid = "35799efe-d79c-43a4-bdab-fb82c48beba3")
        private BigDecimal referenceRetailPriceFour;

        /** 参考零售价五 */
        @AutoGenerated(locked = true, uuid = "765eabfd-a68b-4d37-845b-0a0bbf593466")
        private BigDecimal referenceRetailPriceFive;

        /** 医保支付价 */
        @AutoGenerated(locked = true, uuid = "3e89e5da-8b4f-4a07-9ab5-844269d4e608")
        private BigDecimal insurancePayPrice;
    }

    @Setter
    @Getter
    public static class DrugOriginBatchInventoryBaseVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "79ec12da-8b55-4606-bf9e-0c5574f4e1aa")
        private String id;

        /** 批次id */
        @AutoGenerated(locked = true, uuid = "60d0b5d1-5b2d-4023-a404-a185c8332fea")
        private String batchId;

        /** 药品产地规格id */
        @AutoGenerated(locked = true, uuid = "da988609-0ee2-4471-9b03-4a72880cdb2b")
        private String drugOriginSpecificationId;

        /** 库存ID */
        @AutoGenerated(locked = true, uuid = "dffc9c37-9396-4073-aa76-989a7a55ba55")
        private String inventoryId;

        /** 药品产地编码 冗余存 */
        @AutoGenerated(locked = true, uuid = "59108b98-be95-4cc7-a68e-f40e8505cc8e")
        private String drugOriginCode;

        /** 库房编码 */
        @AutoGenerated(locked = true, uuid = "2be3e247-9144-4012-a097-1baeec3c6fa5")
        private String storageCode;

        /** 可供标识id */
        @AutoGenerated(locked = true, uuid = "0298e666-3efa-4b8c-9928-0a6f8f439657")
        private String supplyId;

        /** 数量 库存数量，按最小规格单位存储 */
        @AutoGenerated(locked = true, uuid = "e03599bf-90c9-45e9-9447-66eca3337d3a")
        private BigDecimal amount;

        /** 批号 */
        @AutoGenerated(locked = true, uuid = "589c7c95-a084-4d77-8fa0-1ffbfecf8a7e")
        private String batchNumber;

        /** 创建时间 */
        @AutoGenerated(locked = true, uuid = "05140046-8a61-4a5a-ac8c-d62546d84540")
        private Date createdAt;

        /** 有效期 批次效期 */
        @AutoGenerated(locked = true, uuid = "7ff55ff0-d3f9-47c0-ba1d-89142daf43e2")
        private Date expirationDate;

        /** gcp编码 gcp药品一物一码，开单发药时也按编码开单发药 */
        @AutoGenerated(locked = true, uuid = "72c75c3a-9a87-4bd4-b36a-67a14de3d7c5")
        private String gcpCode;

        /** 更新时间 */
        @AutoGenerated(locked = true, uuid = "36b67837-d0bb-4690-bc6d-40e066adcea5")
        private Date updatedAt;

        /**
         * 虚库存 虚库存数量增减时，数量字段需要同步增减 虚库存只有药房才启用 使用场景： 1、药房急用，药库来不及出库的药品
         * 2、超发药品（开封有时效的注射剂，开药时按瓶发，但使用时可能多个患者共用一瓶，导致线下库存比线上多）
         * 3、中药饮片/颗粒剂--院内药（医院无实际库存，第三方自己管理库存，开药后通过采购、出入库平账）
         */
        @AutoGenerated(locked = true, uuid = "4e6d99d5-18f6-4197-81f6-5984ea6e2f54")
        private BigDecimal virtualAmount;

        /** 进价 */
        @AutoGenerated(locked = true, uuid = "d4418641-2002-48a6-bd02-31b3821dfade")
        private BigDecimal purchasePrice;

        /** 零售价 */
        @AutoGenerated(locked = true, uuid = "bc5b3004-dc3e-4f55-b124-ec302e32010e")
        private BigDecimal retailPrice;

        /** 进货日期 */
        @AutoGenerated(locked = true, uuid = "94029a31-0cd6-48ac-ad70-6172bd0c629b")
        private Date importDateTime;
    }
}
