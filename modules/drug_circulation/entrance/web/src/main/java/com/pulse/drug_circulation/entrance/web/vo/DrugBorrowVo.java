package com.pulse.drug_circulation.entrance.web.vo;

import com.pulse.dictionary_basic.persist.eo.InputCodeEo;
import com.pulse.drug_circulation.common.enums.BorrowTypeEnum;
import com.pulse.drug_circulation.common.enums.ReturnStatusEnum;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowVo.StaffBaseVo;
import com.pulse.organization.common.enums.GenderEnum;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Setter
@Getter
@AutoGenerated(locked = true, uuid = "09b72806-a240-4212-b243-89ac1d582fdd|VO|DEFINITION")
public class DrugBorrowVo {
    /** 制单人 */
    @Valid
    @AutoGenerated(locked = true, uuid = "853e4e19-54b4-4412-8fc3-6e6cdcdb8c7e")
    private StaffBaseVo applyStaff;

    /** 借药人科室 */
    @AutoGenerated(locked = true, uuid = "9aead55e-df1e-4a84-9df1-a6f4c76cc591")
    private String borrowDepartment;

    /** 还药对应借药单id */
    @AutoGenerated(locked = true, uuid = "22d3ecde-40cc-4bfa-90cb-cc7fb384dd25")
    private String borrowId;

    /** 借还人id */
    @Valid
    @AutoGenerated(locked = true, uuid = "beca47d5-ee3d-41b1-ba9b-785e6415749e")
    private StaffBaseVo borrowStaff;

    /** 借还类型 借药、还药 */
    @AutoGenerated(locked = true, uuid = "2808de0b-c82f-4726-80fa-2e5aa7590805")
    private BorrowTypeEnum borrowType;

    /** 总金额 */
    @AutoGenerated(locked = true, uuid = "8cede848-510e-41d1-9943-51ca0dfd559f")
    private BigDecimal cost;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "cd261951-1ecd-468f-9a05-070b529ded72")
    private Date createdAt;

    /** 借还药明细 */
    @Valid
    @AutoGenerated(locked = true, uuid = "2c04ce74-d124-4fc0-9ac0-dc69bfdc3976")
    private List<DrugBorrowDetailBaseVo> drugBorrowDetailList;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "5dada14a-ac20-4d9f-b22e-bfe3f0faa390")
    private String id;

    /** 出库单号 */
    @AutoGenerated(locked = true, uuid = "c1ecbf96-1bf4-44ed-9a6b-a11368527d3f")
    private String inventoryNumber;

    /** 还药承诺日期 */
    @AutoGenerated(locked = true, uuid = "26672465-171b-4ada-91d2-d30fcfdd981e")
    private Date planReturnDateTime;

    /** 还药批次 */
    @AutoGenerated(locked = true, uuid = "109c59c1-2e05-49cd-9782-45fc52b868ba")
    private Long returnNumber;

    /** 还药状态 未还、部分还、全还 */
    @AutoGenerated(locked = true, uuid = "bcbfe3b8-0ec6-4382-b98c-8cc0bc13ec97")
    private ReturnStatusEnum returnStatus;

    /** 库房编码 借药科室 */
    @AutoGenerated(locked = true, uuid = "1f48a967-13c5-4e77-86b4-847961b73630")
    private String storageCode;

    /** 提交时间 */
    @AutoGenerated(locked = true, uuid = "fa13f30b-eeaf-463c-ada8-df84ce3dc38e")
    private Date submitDateTime;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "f6ebeece-0d47-495b-bfd1-b6c6f90bc514")
    private Date updatedAt;

    @Setter
    @Getter
    public static class StaffBaseVo {
        /** 主键 */
        @AutoGenerated(locked = true, uuid = "deb9dec5-5ec8-4278-ad91-e16d8edfacd3")
        private String id;

        /** 性别 */
        @AutoGenerated(locked = true, uuid = "d7c43327-2880-4448-829f-ee863677933b")
        private GenderEnum gender;

        /** 输入码 */
        @Valid
        @AutoGenerated(locked = true, uuid = "92ccc3ea-1f70-4bbe-93ed-1540301caa6f")
        private InputCodeEo inputCode;

        /** 姓名 */
        @AutoGenerated(locked = true, uuid = "697ec1d7-7699-4cb7-a6e0-4d6eb3ef5e17")
        private String name;

        /** 员工编号 */
        @AutoGenerated(locked = true, uuid = "f228aac7-0172-4e1a-b501-78421e470cad")
        private String staffNumber;

        /** 电话号码 */
        @AutoGenerated(locked = true, uuid = "8d460c02-f1fb-41ff-8f4e-f13b939d7e1b")
        private String phoneNumber;

        /** 手机短号 */
        @AutoGenerated(locked = true, uuid = "e3f70274-f4d6-4493-a384-2b0d033795b5")
        private String shortPhoneNumber;
    }
}
