package com.pulse.drug_circulation.entrance.web.controller;

import com.pulse.drug_circulation.entrance.web.converter.DrugBorrowDetailVoConverter;
import com.pulse.drug_circulation.entrance.web.converter.DrugBorrowVoConverter;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowDetailVo;
import com.pulse.drug_circulation.entrance.web.vo.DrugBorrowVo;
import com.pulse.drug_circulation.manager.dto.DrugBorrowDetailDto;
import com.pulse.drug_circulation.manager.dto.DrugBorrowDto;
import com.pulse.drug_circulation.persist.qto.SearchBorrowQto;
import com.pulse.drug_circulation.service.DrugBorrowBOService;
import com.pulse.drug_circulation.service.DrugBorrowDetailDtoService;
import com.pulse.drug_circulation.service.bto.CreateBorrowAndDetailBto;
import com.pulse.drug_circulation.service.bto.CreateBorrowDetailBto;
import com.pulse.drug_circulation.service.bto.DeleteBorrowBto;
import com.pulse.drug_circulation.service.bto.DeleteBorrowDetailBto;
import com.pulse.drug_circulation.service.bto.MergeBorrowBto;
import com.pulse.drug_circulation.service.bto.MergeBorrowDetailBto;
import com.pulse.drug_circulation.service.bto.MergeDrugBorrowAndDetailBto;
import com.pulse.drug_circulation.service.query.DrugBorrowDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.List;

import javax.annotation.Resource;
import javax.validation.Valid;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "4e2c1e90-2b3c-3adf-8a1d-5c69257d2867")
public class DrugCirculationBorrowController {
    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowBOService drugBorrowBOService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowDetailDtoService drugBorrowDetailDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowDetailVoConverter drugBorrowDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowDtoQueryService drugBorrowDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private DrugBorrowVoConverter drugBorrowVoConverter;

    /** 搜索借还药管理列表 */
    @PublicInterface(id = "0c2d8fa7-84a3-4459-8037-1a617a5d79a5", version = "1747901890180")
    @AutoGenerated(locked = false, uuid = "0c2d8fa7-84a3-4459-8037-1a617a5d79a5")
    @RequestMapping(
            value = {"/api/drug-circulation/search-borrow-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<DrugBorrowVo> searchBorrowPaged(@Valid SearchBorrowQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<DrugBorrowDto> dtoResult = drugBorrowDtoQueryService.searchBorrowPaged(qto);
        VSQueryResult<DrugBorrowVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(drugBorrowVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** merge借还药 */
    @PublicInterface(id = "15705ddf-1cad-41d2-9554-568c600368b3", version = "1748936451702")
    @AutoGenerated(locked = false, uuid = "15705ddf-1cad-41d2-9554-568c600368b3")
    @RequestMapping(
            value = {"/api/drug-circulation/merge-drug-borrow-and-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeDrugBorrowAndDetail(
            @Valid MergeDrugBorrowAndDetailBto mergeDrugBorrowAndDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugBorrowBOService.mergeDrugBorrowAndDetail(mergeDrugBorrowAndDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** merge借还药 */
    @PublicInterface(id = "15edd7c8-adde-4423-bc64-8f87e83db8c6", version = "1747904333137")
    @AutoGenerated(locked = false, uuid = "15edd7c8-adde-4423-bc64-8f87e83db8c6")
    @RequestMapping(
            value = {"/api/drug-circulation/merge-borrow"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeBorrow(@Valid MergeBorrowBto mergeBorrowBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugBorrowBOService.mergeBorrow(mergeBorrowBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 批量merge借还药 */
    @PublicInterface(id = "34f42b2a-ea14-4d8e-bbe9-40230b9652e5", version = "1749026904730")
    @AutoGenerated(locked = false, uuid = "34f42b2a-ea14-4d8e-bbe9-40230b9652e5")
    @RequestMapping(
            value = {"/api/drug-circulation/merge-drug-borrow-and-detail-batch"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeDrugBorrowAndDetailBatch(@Valid List<MergeBorrowBto> merge) {
        // TODO implement method
        String result = "";
        for (MergeBorrowBto item : merge) {
            result = drugBorrowBOService.mergeBorrow(item);
        }
        return result;
    }

    /** 更新借还药 */
    @PublicInterface(id = "42a5a126-28ae-4228-a7a7-42c35afbacec", version = "1747904237586")
    @AutoGenerated(locked = false, uuid = "42a5a126-28ae-4228-a7a7-42c35afbacec")
    @RequestMapping(
            value = {"/api/drug-circulation/merge-borrow-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeBorrowDetail(
            @Valid MergeBorrowDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugBorrowBOService.mergeBorrowDetail(drugBorrowDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 删除借还药 */
    @PublicInterface(id = "84a3a0c6-7467-4b49-9163-a41b5aac3774", version = "1747904201619")
    @AutoGenerated(locked = false, uuid = "84a3a0c6-7467-4b49-9163-a41b5aac3774")
    @RequestMapping(
            value = {"/api/drug-circulation/delete-borrow"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteBorrow(@Valid DeleteBorrowBto deleteBorrowBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugBorrowBOService.deleteBorrow(deleteBorrowBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 创建借还药明细 */
    @PublicInterface(id = "a52ec69b-8b11-4b19-9600-2f3814a70b1f", version = "1747904274490")
    @AutoGenerated(locked = false, uuid = "a52ec69b-8b11-4b19-9600-2f3814a70b1f")
    @RequestMapping(
            value = {"/api/drug-circulation/create-borrow-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createBorrowDetail(
            @Valid CreateBorrowDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugBorrowBOService.createBorrowDetail(drugBorrowDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 新建借还药带明细 */
    @PublicInterface(id = "ba401eea-7a9b-4c87-8ca1-89f78ada9099", version = "1747904358856")
    @AutoGenerated(locked = false, uuid = "ba401eea-7a9b-4c87-8ca1-89f78ada9099")
    @RequestMapping(
            value = {"/api/drug-circulation/create-borrow-and-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createBorrowAndDetail(@Valid CreateBorrowAndDetailBto createBorrowAndDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugBorrowBOService.createBorrowAndDetail(createBorrowAndDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 删除借还药明细 */
    @PublicInterface(id = "c0aa1487-80a3-4c6e-8908-55f4560dd67c", version = "1747904082466")
    @AutoGenerated(locked = false, uuid = "c0aa1487-80a3-4c6e-8908-55f4560dd67c")
    @RequestMapping(
            value = {"/api/drug-circulation/delete-borrow-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteBorrowDetail(
            @Valid DeleteBorrowDetailBto.DrugBorrowDetailBto drugBorrowDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = drugBorrowBOService.deleteBorrowDetail(drugBorrowDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据还药对应借药明细id获取药物借还明细列表 */
    @PublicInterface(id = "c5ae169e-46e2-45c0-8e6d-7e610a08bf26", version = "1749088711676")
    @AutoGenerated(locked = false, uuid = "c5ae169e-46e2-45c0-8e6d-7e610a08bf26")
    @RequestMapping(
            value = {"/api/drug-circulation/get-by-drug-borrow-detail-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<DrugBorrowDetailVo> getByDrugBorrowDetailId(String drugBorrowDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugBorrowDetailDto> rpcResult =
                drugBorrowDetailDtoService.getByDrugBorrowDetailId(drugBorrowDetailId);
        List<DrugBorrowDetailVo> result =
                drugBorrowDetailVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据还药对应借药明细id获取药物借还明细列表 */
    @PublicInterface(id = "c9262bf8-02ec-40b3-941f-124bfacd7eb6", version = "1749088684940")
    @AutoGenerated(locked = false, uuid = "c9262bf8-02ec-40b3-941f-124bfacd7eb6")
    @RequestMapping(
            value = {"/api/drug-circulation/get-by-drug-borrow-detail-id-list"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<DrugBorrowDetailVo> getByDrugBorrowDetailIdList(
            @Valid List<String> drugBorrowDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugBorrowDetailDto> rpcResult =
                drugBorrowDetailDtoService.getByDrugBorrowDetailIds(drugBorrowDetailId);
        List<DrugBorrowDetailVo> result =
                drugBorrowDetailVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
