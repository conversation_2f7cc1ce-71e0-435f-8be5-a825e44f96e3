package com.pulse.drug_circulation.manager.facade.drug_dictionary.base;

import com.fasterxml.jackson.core.type.TypeReference;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationWithDrugOriginDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.RpcMethodExecutor;
import com.vs.common.util.rpc.router.RpcEnvContext;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.annotation.Resource;

@AutoGenerated(locked = true, uuid = "9f134e06-43ee-3dcd-9f15-6c39456b8a2e")
public class DrugOriginSpecificationWithDrugOriginDtoServiceInDrugCirculationBaseRpcAdapter {
    @AutoGenerated(locked = true)
    @Resource
    private RpcMethodExecutor rpcMethodExecutor;

    @AutoGenerated(locked = true, uuid = "1cc0fce0-0dbd-4593-a4bb-26a4ba7746b2|RPC|BASE_ADAPTER")
    public List<DrugOriginSpecificationWithDrugOriginDto> getByIds(List<String> id) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("id", id);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("id", List.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/drug_dictionary/1cc0fce0-0dbd-4593-a4bb-26a4ba7746b2/DrugOriginSpecificationWithDrugOriginDtoService-getByIds",
                        "com.pulse.drug_dictionary.service.DrugOriginSpecificationWithDrugOriginDtoService",
                        "getByIds",
                        paramMap,
                        paramTypeMap,
                        "f7024bce-ba2c-493b-88a1-0404a9b21686",
                        "8abe848a-da76-4713-8181-5bc6832bf785"),
                new TypeReference<>() {});
    }

    @AutoGenerated(locked = true, uuid = "50737c58-d9fc-4e42-a12d-fc51da394fe3|RPC|BASE_ADAPTER")
    public DrugOriginSpecificationWithDrugOriginDto getById(String id) {
        TreeMap<String, Object> paramMap = new TreeMap<>();
        paramMap.put("id", id);
        Map<String, Class> paramTypeMap = new LinkedHashMap();
        paramTypeMap.put("id", String.class);
        return rpcMethodExecutor.execute(
                new RpcMethodExecutor.RpcExecutorParams(
                        RpcEnvContext.getCurrentHost()
                                + "/rpc/drug_dictionary/50737c58-d9fc-4e42-a12d-fc51da394fe3/DrugOriginSpecificationWithDrugOriginDtoService-getById",
                        "com.pulse.drug_dictionary.service.DrugOriginSpecificationWithDrugOriginDtoService",
                        "getById",
                        paramMap,
                        paramTypeMap,
                        "f7024bce-ba2c-493b-88a1-0404a9b21686",
                        "8abe848a-da76-4713-8181-5bc6832bf785"),
                new TypeReference<>() {});
    }
}
