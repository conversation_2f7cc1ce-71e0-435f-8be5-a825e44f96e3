package com.pulse.drug_circulation.manager.dto;

import com.pulse.drug_circulation.common.enums.ReturnStatusEnum;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationWithDrugOriginDto;
import com.pulse.drug_inventory.manager.dto.DrugOriginBatchInventoryBaseDto;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "334fa8b6-387f-488f-b8be-e59b0dfa5d9c|DTO|DEFINITION")
public class DrugBorrowDetailDto {
    /** 数量 */
    @AutoGenerated(locked = true, uuid = "bdaacd9f-e6a5-424e-a4d5-60829c3593f0")
    private Long amount;

    /** 库存id */
    @Valid
    @AutoGenerated(locked = true, uuid = "8d83632d-ad0a-4f02-86d9-618c74a268e5")
    private DrugOriginBatchInventoryBaseDto batchInventory;

    /** 金额 */
    @AutoGenerated(locked = true, uuid = "e4954749-118e-434c-b9b2-18f74fc3540b")
    private BigDecimal cost;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "fd371f07-d9ff-466a-a465-89e4dc17c879")
    private Date createdAt;

    /** 借还药id */
    @Valid
    @AutoGenerated(locked = true, uuid = "af4ab540-3868-4f43-a0a7-46de81ef4888")
    private DrugBorrowBaseDto drugBorrow;

    /** 还药对应借药明细id */
    @Valid
    @AutoGenerated(locked = true, uuid = "abc61464-7331-4f59-95a6-b04d4631a06f")
    private DrugBorrowDetailBaseDto drugBorrowDetail;

    /** 还药明细 */
    @Valid
    @AutoGenerated(locked = true, uuid = "b801787e-d5e7-4ea8-8e56-63157d37e2ac")
    private List<DrugBorrowDetailBaseDto> drugBorrowDetailList;

    /** 药物名称 */
    @AutoGenerated(locked = true, uuid = "be7bc745-d17d-4f8e-b3ad-7d1abb8e25ec")
    private String drugName;

    /** 药品产地规格id */
    @Valid
    @AutoGenerated(locked = true, uuid = "5046eae0-62d6-4b27-80ca-46a70aa0741e")
    private DrugOriginSpecificationWithDrugOriginDto drugOriginSpecification;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "efaffe29-590c-4f89-8950-2534ac566f17")
    private String id;

    /** 单价 */
    @AutoGenerated(locked = true, uuid = "662f8d7e-cbb9-4701-a784-f68a7b30dada")
    private BigDecimal price;

    /** 进价 */
    @AutoGenerated(locked = true, uuid = "10bc9bd3-2a37-4e9f-8054-e7c5982d04b7")
    private BigDecimal purchasePrice;

    /** 还药状态 未还、部分还、全还 */
    @AutoGenerated(locked = true, uuid = "8bf792c2-0ced-4c8a-8802-552fb0006e91")
    private ReturnStatusEnum returnStatus;

    /** 已还药数量 */
    @AutoGenerated(locked = true, uuid = "aaf35f25-083d-49a3-878e-19906cd0f8ca")
    private Long returnedAmount;

    /** 排序号 */
    @AutoGenerated(locked = true, uuid = "9f71fb01-74b3-425b-a797-78002e433ccf")
    private Long sortNumber;

    /** 库存数量 */
    @AutoGenerated(locked = true, uuid = "5ef6dad0-010d-499c-b66f-c59ad2945983")
    private BigDecimal stock;

    /** 单位 */
    @AutoGenerated(locked = true, uuid = "d2ff9848-a174-4e52-83c2-cf10b665fea8")
    private String unit;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "4a7bbe31-001d-4a21-b250-a555212d8924")
    private Date updatedAt;
}
