package com.pulse.drug_circulation.manager.facade.drug_dictionary;

import com.pulse.drug_circulation.manager.facade.drug_dictionary.base.DrugOriginSpecificationWithDrugOriginDtoServiceInDrugCirculationBaseRpcAdapter;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationWithDrugOriginDto;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.adapter.RpcRefer;
import com.vs.common.util.rpc.adapter.RpcStubClass;

import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RpcStubClass(
        projectId = "ad09c67e-87d7-47a0-bff1-fde6f72fdd1d",
        moduleId = "f7024bce-ba2c-493b-88a1-0404a9b21686")
@AutoGenerated(locked = false, uuid = "23ae989b-a6ce-3282-9b88-e08c423f5194")
public class DrugOriginSpecificationWithDrugOriginDtoServiceInDrugCirculationRpcAdapter
        extends DrugOriginSpecificationWithDrugOriginDtoServiceInDrugCirculationBaseRpcAdapter {

    @RpcRefer(id = "1cc0fce0-0dbd-4593-a4bb-26a4ba7746b2", version = "1748420709621")
    @AutoGenerated(locked = false, uuid = "1cc0fce0-0dbd-4593-a4bb-26a4ba7746b2|RPC|ADAPTER")
    public List<DrugOriginSpecificationWithDrugOriginDto> getByIds(List<String> id) {
        return super.getByIds(id);
    }

    @RpcRefer(id = "50737c58-d9fc-4e42-a12d-fc51da394fe3", version = "1748420709596")
    @AutoGenerated(locked = false, uuid = "50737c58-d9fc-4e42-a12d-fc51da394fe3|RPC|ADAPTER")
    public DrugOriginSpecificationWithDrugOriginDto getById(String id) {
        return super.getById(id);
    }
}
