package com.pulse.drug_circulation.manager.impl.base;

import cn.hutool.core.collection.CollectionUtil;

import com.google.common.collect.Lists;
import com.pulse.drug_circulation.manager.DrugBorrowBaseDtoManager;
import com.pulse.drug_circulation.manager.DrugBorrowDetailBaseDtoManager;
import com.pulse.drug_circulation.manager.DrugBorrowDetailDtoManager;
import com.pulse.drug_circulation.manager.converter.DrugBorrowDetailBaseDtoConverter;
import com.pulse.drug_circulation.manager.converter.DrugBorrowDetailDtoConverter;
import com.pulse.drug_circulation.manager.dto.DrugBorrowBaseDto;
import com.pulse.drug_circulation.manager.dto.DrugBorrowDetailBaseDto;
import com.pulse.drug_circulation.manager.dto.DrugBorrowDetailDto;
import com.pulse.drug_circulation.manager.facade.drug_dictionary.DrugOriginSpecificationWithDrugOriginDtoServiceInDrugCirculationRpcAdapter;
import com.pulse.drug_circulation.manager.facade.drug_inventory.DrugOriginBatchInventoryBaseDtoServiceInDrugCirculationRpcAdapter;
import com.pulse.drug_circulation.persist.dos.DrugBorrowDetail;
import com.pulse.drug_circulation.persist.mapper.DrugBorrowDetailDao;
import com.pulse.drug_dictionary.manager.dto.DrugOriginSpecificationWithDrugOriginDto;
import com.pulse.drug_inventory.manager.dto.DrugOriginBatchInventoryBaseDto;
import com.vs.code.AutoGenerated;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@AutoGenerated(locked = true, uuid = "334fa8b6-387f-488f-b8be-e59b0dfa5d9c|DTO|BASE_MANAGER_IMPL")
public abstract class DrugBorrowDetailDtoManagerBaseImpl implements DrugBorrowDetailDtoManager {
    @AutoGenerated(locked = true)
    @Autowired
    private DrugBorrowBaseDtoManager drugBorrowBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugBorrowDetailBaseDtoConverter drugBorrowDetailBaseDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugBorrowDetailBaseDtoManager drugBorrowDetailBaseDtoManager;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugBorrowDetailDao drugBorrowDetailDao;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugBorrowDetailDtoConverter drugBorrowDetailDtoConverter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginBatchInventoryBaseDtoServiceInDrugCirculationRpcAdapter
            drugOriginBatchInventoryBaseDtoServiceInDrugCirculationRpcAdapter;

    @AutoGenerated(locked = true)
    @Autowired
    private DrugOriginSpecificationWithDrugOriginDtoServiceInDrugCirculationRpcAdapter
            drugOriginSpecificationWithDrugOriginDtoServiceInDrugCirculationRpcAdapter;

    @AutoGenerated(locked = true, uuid = "2171451e-2d17-3bd3-b696-95c1efe3b9fc")
    @Override
    public List<DrugBorrowDetailDto> getByDrugBorrowId(String drugBorrowId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugBorrowDetailDto> drugBorrowDetailDtoList =
                getByDrugBorrowIds(Arrays.asList(drugBorrowId));
        return drugBorrowDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "2f703b21-f3a4-34e0-b6eb-407caa957686")
    @Override
    public List<DrugBorrowDetailDto> getByDrugOriginSpecificationId(
            String drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugBorrowDetailDto> drugBorrowDetailDtoList =
                getByDrugOriginSpecificationIds(Arrays.asList(drugOriginSpecificationId));
        return drugBorrowDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "3cadc05c-4e65-368a-91d3-a946fe7a4e7d")
    @Override
    public List<DrugBorrowDetailDto> getByBatchInventoryId(String batchInventoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugBorrowDetailDto> drugBorrowDetailDtoList =
                getByBatchInventoryIds(Arrays.asList(batchInventoryId));
        return drugBorrowDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "5fd68671-202f-3f65-92d7-f11c16b9b102")
    @Override
    public List<DrugBorrowDetailDto> getByDrugBorrowDetailIds(List<String> drugBorrowDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugBorrowDetailId)) {
            return Collections.emptyList();
        }

        List<DrugBorrowDetail> drugBorrowDetailList =
                drugBorrowDetailDao.getByDrugBorrowDetailIds(drugBorrowDetailId);
        if (CollectionUtil.isEmpty(drugBorrowDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugBorrowDetailToDrugBorrowDetailDto(drugBorrowDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "65c4052f-ad7c-3a84-8957-153f1d6ab61c")
    @Override
    public List<DrugBorrowDetailDto> getByBatchInventoryIds(List<String> batchInventoryId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(batchInventoryId)) {
            return Collections.emptyList();
        }

        List<DrugBorrowDetail> drugBorrowDetailList =
                drugBorrowDetailDao.getByBatchInventoryIds(batchInventoryId);
        if (CollectionUtil.isEmpty(drugBorrowDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugBorrowDetailToDrugBorrowDetailDto(drugBorrowDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "74068013-639d-3e92-91ba-4ad350e9a0aa")
    public List<DrugBorrowDetailDto> doConvertFromDrugBorrowDetailToDrugBorrowDetailDto(
            List<DrugBorrowDetail> drugBorrowDetailList) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugBorrowDetailList)) {
            return Collections.emptyList();
        }

        Map<String, String> drugBorrowDetailIdMap =
                drugBorrowDetailList.stream()
                        .filter(i -> i.getDrugBorrowDetailId() != null)
                        .collect(
                                Collectors.toMap(
                                        DrugBorrowDetail::getId,
                                        DrugBorrowDetail::getDrugBorrowDetailId));
        List<DrugBorrowDetailBaseDto> drugBorrowDetailIdDrugBorrowDetailBaseDtoList =
                drugBorrowDetailBaseDtoManager.getByIds(
                        new ArrayList<>(new HashSet<>(drugBorrowDetailIdMap.values())));
        Map<String, DrugBorrowDetailBaseDto> drugBorrowDetailIdDrugBorrowDetailBaseDtoMapRaw =
                drugBorrowDetailIdDrugBorrowDetailBaseDtoList.stream()
                        .collect(Collectors.toMap(DrugBorrowDetailBaseDto::getId, i -> i));
        Map<String, DrugBorrowDetailBaseDto> drugBorrowDetailIdDrugBorrowDetailBaseDtoMap =
                drugBorrowDetailIdMap.entrySet().stream()
                        .filter(
                                i ->
                                        drugBorrowDetailIdDrugBorrowDetailBaseDtoMapRaw.get(
                                                        i.getValue())
                                                != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i ->
                                                drugBorrowDetailIdDrugBorrowDetailBaseDtoMapRaw.get(
                                                        i.getValue())));
        Map<String, String> drugBorrowIdMap =
                drugBorrowDetailList.stream()
                        .filter(i -> i.getDrugBorrowId() != null)
                        .collect(
                                Collectors.toMap(
                                        DrugBorrowDetail::getId,
                                        DrugBorrowDetail::getDrugBorrowId));
        List<DrugBorrowBaseDto> drugBorrowIdDrugBorrowBaseDtoList =
                drugBorrowBaseDtoManager.getByIds(
                        new ArrayList<>(new HashSet<>(drugBorrowIdMap.values())));
        Map<String, DrugBorrowBaseDto> drugBorrowIdDrugBorrowBaseDtoMapRaw =
                drugBorrowIdDrugBorrowBaseDtoList.stream()
                        .collect(Collectors.toMap(DrugBorrowBaseDto::getId, i -> i));
        Map<String, DrugBorrowBaseDto> drugBorrowIdDrugBorrowBaseDtoMap =
                drugBorrowIdMap.entrySet().stream()
                        .filter(i -> drugBorrowIdDrugBorrowBaseDtoMapRaw.get(i.getValue()) != null)
                        .collect(
                                Collectors.toMap(
                                        i -> i.getKey(),
                                        i ->
                                                drugBorrowIdDrugBorrowBaseDtoMapRaw.get(
                                                        i.getValue())));
        Map<String, String> drugOriginSpecificationIdMap =
                drugBorrowDetailList.stream()
                        .filter(i -> i.getDrugOriginSpecificationId() != null)
                        .collect(
                                Collectors.toMap(
                                        DrugBorrowDetail::getId,
                                        DrugBorrowDetail::getDrugOriginSpecificationId));
        List<DrugOriginSpecificationWithDrugOriginDto>
                drugOriginSpecificationIdDrugOriginSpecificationWithDrugOriginDtoList =
                        drugOriginSpecificationWithDrugOriginDtoServiceInDrugCirculationRpcAdapter
                                .getByIds(
                                        new ArrayList<>(
                                                new HashSet<>(
                                                        drugOriginSpecificationIdMap.values())));
        Map<String, DrugOriginSpecificationWithDrugOriginDto>
                drugOriginSpecificationIdDrugOriginSpecificationWithDrugOriginDtoMapRaw =
                        drugOriginSpecificationIdDrugOriginSpecificationWithDrugOriginDtoList
                                .stream()
                                .collect(
                                        Collectors.toMap(
                                                DrugOriginSpecificationWithDrugOriginDto::getId,
                                                i -> i));
        Map<String, DrugOriginSpecificationWithDrugOriginDto>
                drugOriginSpecificationIdDrugOriginSpecificationWithDrugOriginDtoMap =
                        drugOriginSpecificationIdMap.entrySet().stream()
                                .filter(
                                        i ->
                                                drugOriginSpecificationIdDrugOriginSpecificationWithDrugOriginDtoMapRaw
                                                                .get(i.getValue())
                                                        != null)
                                .collect(
                                        Collectors.toMap(
                                                i -> i.getKey(),
                                                i ->
                                                        drugOriginSpecificationIdDrugOriginSpecificationWithDrugOriginDtoMapRaw
                                                                .get(i.getValue())));
        Map<String, String> batchInventoryIdMap =
                drugBorrowDetailList.stream()
                        .filter(i -> i.getBatchInventoryId() != null)
                        .collect(
                                Collectors.toMap(
                                        DrugBorrowDetail::getId,
                                        DrugBorrowDetail::getBatchInventoryId));
        List<DrugOriginBatchInventoryBaseDto> batchInventoryIdDrugOriginBatchInventoryBaseDtoList =
                drugOriginBatchInventoryBaseDtoServiceInDrugCirculationRpcAdapter.getByIds(
                        new ArrayList<>(new HashSet<>(batchInventoryIdMap.values())));
        Map<String, DrugOriginBatchInventoryBaseDto>
                batchInventoryIdDrugOriginBatchInventoryBaseDtoMapRaw =
                        batchInventoryIdDrugOriginBatchInventoryBaseDtoList.stream()
                                .collect(
                                        Collectors.toMap(
                                                DrugOriginBatchInventoryBaseDto::getId, i -> i));
        Map<String, DrugOriginBatchInventoryBaseDto>
                batchInventoryIdDrugOriginBatchInventoryBaseDtoMap =
                        batchInventoryIdMap.entrySet().stream()
                                .filter(
                                        i ->
                                                batchInventoryIdDrugOriginBatchInventoryBaseDtoMapRaw
                                                                .get(i.getValue())
                                                        != null)
                                .collect(
                                        Collectors.toMap(
                                                i -> i.getKey(),
                                                i ->
                                                        batchInventoryIdDrugOriginBatchInventoryBaseDtoMapRaw
                                                                .get(i.getValue())));

        List<DrugBorrowDetailBaseDto> drugBorrowDetailBaseDtoList =
                drugBorrowDetailBaseDtoManager.getByDrugBorrowDetailIds(
                        drugBorrowDetailList.stream()
                                .map(i -> i.getId())
                                .collect(Collectors.toList()));
        Map<String, List<DrugBorrowDetailBaseDto>> idDrugBorrowDetailBaseDtoListMap =
                drugBorrowDetailBaseDtoList.stream()
                        .collect(
                                Collectors.toMap(
                                        i -> i.getDrugBorrowDetailId(),
                                        Lists::newArrayList,
                                        (l1, l2) -> {
                                            l1.addAll(l2);
                                            return l1;
                                        }));

        List<DrugBorrowDetailBaseDto> baseDtoList =
                drugBorrowDetailBaseDtoConverter
                        .convertFromDrugBorrowDetailToDrugBorrowDetailBaseDto(drugBorrowDetailList);
        Map<String, DrugBorrowDetailDto> dtoMap =
                drugBorrowDetailDtoConverter
                        .convertFromDrugBorrowDetailBaseDtoToDrugBorrowDetailDto(baseDtoList)
                        .stream()
                        .collect(
                                Collectors.toMap(
                                        DrugBorrowDetailDto::getId,
                                        Function.identity(),
                                        (o1, o2) -> o1));

        List<DrugBorrowDetailDto> drugBorrowDetailDtoList = new ArrayList<>();
        for (DrugBorrowDetail i : drugBorrowDetailList) {
            DrugBorrowDetailDto drugBorrowDetailDto = dtoMap.get(i.getId());
            if (drugBorrowDetailDto == null) {
                continue;
            }

            if (null != i.getDrugBorrowDetailId()) {
                drugBorrowDetailDto.setDrugBorrowDetail(
                        drugBorrowDetailIdDrugBorrowDetailBaseDtoMap.getOrDefault(i.getId(), null));
            }
            if (null != i.getDrugBorrowId()) {
                drugBorrowDetailDto.setDrugBorrow(
                        drugBorrowIdDrugBorrowBaseDtoMap.getOrDefault(i.getId(), null));
            }
            if (null != i.getDrugOriginSpecificationId()) {
                drugBorrowDetailDto.setDrugOriginSpecification(
                        drugOriginSpecificationIdDrugOriginSpecificationWithDrugOriginDtoMap
                                .getOrDefault(i.getId(), null));
            }
            if (null != i.getBatchInventoryId()) {
                drugBorrowDetailDto.setBatchInventory(
                        batchInventoryIdDrugOriginBatchInventoryBaseDtoMap.getOrDefault(
                                i.getId(), null));
            }
            if (null != i.getId()) {
                drugBorrowDetailDto.setDrugBorrowDetailList(
                        idDrugBorrowDetailBaseDtoListMap.getOrDefault(
                                i.getId(), Collections.emptyList()));
            }
            /** This block is generated by vs, do not modify, end anchor 1 */

            /** This block is generated by vs, do not modify, start anchor 2 */
            drugBorrowDetailDtoList.add(drugBorrowDetailDto);
        }
        return drugBorrowDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    @AutoGenerated(locked = true, uuid = "86ec513e-2541-3aa8-9d81-369acd104874")
    @Override
    public List<DrugBorrowDetailDto> getByDrugOriginSpecificationIds(
            List<String> drugOriginSpecificationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugOriginSpecificationId)) {
            return Collections.emptyList();
        }

        List<DrugBorrowDetail> drugBorrowDetailList =
                drugBorrowDetailDao.getByDrugOriginSpecificationIds(drugOriginSpecificationId);
        if (CollectionUtil.isEmpty(drugBorrowDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugBorrowDetailToDrugBorrowDetailDto(drugBorrowDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b6b12b3c-6061-3036-9b0a-2dc24c15c5ab")
    @Override
    public DrugBorrowDetailDto getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugBorrowDetailDto> ret = getByIds(Arrays.asList(id));
        if (ret.size() > 1) {
            throw new IgnoredException(ErrorCode.SYS_ERROR, "返回值个数超过1个");
        }
        DrugBorrowDetailDto drugBorrowDetailDto = CollectionUtil.isEmpty(ret) ? null : ret.get(0);
        return drugBorrowDetailDto;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "b98b3852-5edc-3b3a-8f6e-64e2765285ab")
    @Override
    public List<DrugBorrowDetailDto> getByDrugBorrowDetailId(String drugBorrowDetailId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<DrugBorrowDetailDto> drugBorrowDetailDtoList =
                getByDrugBorrowDetailIds(Arrays.asList(drugBorrowDetailId));
        return drugBorrowDetailDtoList;
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "c79d1c5c-1e2e-3fd7-a61b-7c1507b1d8bf")
    @Override
    public List<DrugBorrowDetailDto> getByDrugBorrowIds(List<String> drugBorrowId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(drugBorrowId)) {
            return Collections.emptyList();
        }

        List<DrugBorrowDetail> drugBorrowDetailList =
                drugBorrowDetailDao.getByDrugBorrowIds(drugBorrowId);
        if (CollectionUtil.isEmpty(drugBorrowDetailList)) {
            return Collections.emptyList();
        }

        return doConvertFromDrugBorrowDetailToDrugBorrowDetailDto(drugBorrowDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }

    @AutoGenerated(locked = true, uuid = "d3eee75d-a2d4-337a-9b05-2d70cdba4c4c")
    @Override
    public List<DrugBorrowDetailDto> getByIds(List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        if (CollectionUtil.isEmpty(id)) {
            return Collections.emptyList();
        }

        List<DrugBorrowDetail> drugBorrowDetailList = drugBorrowDetailDao.getByIds(id);
        if (CollectionUtil.isEmpty(drugBorrowDetailList)) {
            return Collections.emptyList();
        }

        // 结果和入参顺序保持一致
        Map<String, DrugBorrowDetail> drugBorrowDetailMap =
                drugBorrowDetailList.stream().collect(Collectors.toMap(i -> i.getId(), i -> i));
        drugBorrowDetailList =
                id.stream()
                        .map(i -> drugBorrowDetailMap.get(i))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

        return doConvertFromDrugBorrowDetailToDrugBorrowDetailDto(drugBorrowDetailList);
        /** This block is generated by vs, do not modify, end anchor 1 */
    }
}
