package com.pulse.drug_dictionary.manager.dto;

import com.pulse.drug_dictionary.common.enums.SpecificationTypeEnum;
import com.vs.code.AutoGenerated;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

import javax.validation.Valid;

@Data
@AutoGenerated(locked = false, uuid = "08ce8f2d-befc-4857-b84f-a26aaef8c5ea|DTO|DEFINITION")
public class DrugOriginSpecificationWithDrugOriginDto {
    /** 拆分系数 当前规格可以拆分出多少个最小规格 */
    @AutoGenerated(locked = true, uuid = "c621edf8-0057-45eb-9cf4-0bfea25c4c1f")
    private Long amountPerPackage;

    /** 招标进价 */
    @AutoGenerated(locked = true, uuid = "07383764-c9f5-4279-9fed-ee3beba6f3c4")
    private BigDecimal bidPurchasePrice;

    /** 创建时间 */
    @AutoGenerated(locked = true, uuid = "a26bf85c-b8e9-4ab7-a95d-7a328e254b9a")
    private Date createdAt;

    /** 是否标准规格 药库/药房默认使用规格 */
    @AutoGenerated(locked = true, uuid = "69f80011-c490-4780-917f-82c57372cf82")
    private Boolean defaultUsedFlag;

    /** 删除时间 */
    @AutoGenerated(locked = true, uuid = "4ee6d470-02c5-422f-a202-c74b4098b2ce")
    private Long deletedAt;

    /** 药品产地 */
    @Valid
    @AutoGenerated(locked = true, uuid = "6a65cddb-2e62-42e7-acb8-060a73f2107c")
    private DrugOriginBaseDto drugOrigin;

    /** 规格 */
    @AutoGenerated(locked = true, uuid = "c57291dd-d53e-4f32-b0df-d0147a445f74")
    private String drugSpecification;

    /** 药品规格明细id */
    @AutoGenerated(locked = true, uuid = "735a5c1b-aa84-4e27-9183-6c332f5e9fc9")
    private String drugSpecificationDetailId;

    /** 主键 */
    @AutoGenerated(locked = true, uuid = "bb8069be-2a66-40ff-b99d-f183ac71a16d")
    private String id;

    /** 医保支付价 */
    @AutoGenerated(locked = true, uuid = "a29ca876-e979-45fc-8b4d-b1c710b2dc3e")
    private BigDecimal insurancePayPrice;

    /** 价表项目编码 价表唯一编码（非id） */
    @AutoGenerated(locked = true, uuid = "828477c1-1759-4415-ae6a-8b004ed529bd")
    private String priceItemCode;

    /** 参考零售价五 */
    @AutoGenerated(locked = true, uuid = "238aa3c8-72a0-44bf-bcd9-7f93d19c7d18")
    private BigDecimal referenceRetailPriceFive;

    /** 参考零售价四 */
    @AutoGenerated(locked = true, uuid = "4f56d079-00fc-44b5-91b0-59aebadee0ef")
    private BigDecimal referenceRetailPriceFour;

    /** 参考零售价一 */
    @AutoGenerated(locked = true, uuid = "bb3127ab-b186-4240-a7eb-76c29f31f11f")
    private BigDecimal referenceRetailPriceOne;

    /** 参考零售价三 */
    @AutoGenerated(locked = true, uuid = "db9805ed-8794-427f-839a-5de7cf9c3add")
    private BigDecimal referenceRetailPriceThree;

    /** 参考零售价二 */
    @AutoGenerated(locked = true, uuid = "14a1840d-9d88-4116-b0b2-20a57318279f")
    private BigDecimal referenceRetailPriceTow;

    /** 规格类型 包装规格、最小规格、其他拆分规格 */
    @AutoGenerated(locked = true, uuid = "ae3caab8-0d0d-4cd4-85cf-37b4a703757b")
    private SpecificationTypeEnum specificationType;

    /** 单位 */
    @AutoGenerated(locked = true, uuid = "bdddda91-b6a3-4d92-8e4f-91018d70c5ef")
    private String unit;

    /** 更新时间 */
    @AutoGenerated(locked = true, uuid = "1bc035fe-4f8b-4836-a89a-ad95f536ee07")
    private Date updatedAt;
}
