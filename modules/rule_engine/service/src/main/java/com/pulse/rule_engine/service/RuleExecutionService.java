package com.pulse.rule_engine.service;

import com.pulse.rule_engine.manager.RuleBaseDtoManager;
import com.pulse.rule_engine.manager.RuleVersionBaseDtoManager;
import com.pulse.rule_engine.manager.dto.RuleBaseDto;
import com.pulse.rule_engine.manager.dto.RuleVersionBaseDto;
import com.pulse.rule_engine.persist.dos.RuleVersion;
import com.pulse.rule_engine.persist.eo.RuleVersionEo;
import com.pulse.rule_engine.manager.adapter.RuleEngineAdapter;
import com.pulse.rule_engine.manager.adapter.RuleEngineAdapterFactory;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.ox.common.exception.ErrorCode;
import com.vs.ox.common.exception.IgnoredException;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** Auto generated Service */
@Slf4j
@Controller
@Validated
@AutoGenerated(locked = false, uuid = "9ec75136-5fa2-3923-8f8d-4f434d2c8b75")
public class RuleExecutionService {

    @Autowired private RuleEngineAdapterFactory ruleEngineAdapterFactory;

    @Autowired private RuleVersionBaseDtoManager ruleVersionBaseDtoManager;

    @Autowired private RuleBaseDtoManager ruleBaseDtoManager;

    /** 检查多个规则是否存在逻辑冲突：检查传入的规则版本列表的逻辑冲突，如只有规则ID，则取该规则最新的规则版本 */
    @PublicInterface(
            id = "285ec1c8-9a55-4e48-90b5-a19eea9d7cab",
            module = "rule_engine",
            moduleId = "e03ca7ad-ee08-468d-9c8a-1b7d14950192",
            version = "1748916606529",
            pubRpc = true)
    @AutoGenerated(locked = false, uuid = "285ec1c8-9a55-4e48-90b5-a19eea9d7cab")
    public Boolean checkRuleConflict(
            @Valid @NotNull List<RuleVersionEo> ruleVersionList, @NotNull String businessData) {
        if (CollectionUtils.isEmpty(ruleVersionList) || !StringUtils.hasText(businessData)) {
            log.warn("规则版本列表为空或业务数据为空，无法进行冲突检查");
            return false;
        }

        try {
            log.info("开始检查规则冲突，规则版本数量: {}", ruleVersionList.size());

            // 获取完整的规则版本信息
            List<RuleVersionBaseDto> completeRuleVersionList =
                    getCompleteRuleVersionList(ruleVersionList);

            if (CollectionUtils.isEmpty(completeRuleVersionList)) {
                log.warn("未找到有效的规则版本信息");
                return false;
            }

            // 获取规则引擎适配器
            RuleEngineAdapter ruleEngineAdapter = ruleEngineAdapterFactory.getDefaultAdapter();

            // 执行冲突检查，传入业务数据
            Boolean hasConflict =
                    ruleEngineAdapter.checkRuleConflict(completeRuleVersionList, businessData);

            log.info("规则冲突检查完成，结果: {}", hasConflict ? "存在冲突" : "无冲突");
            return hasConflict;

        } catch (Exception e) {
            log.error("规则冲突检查失败", e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "规则冲突检查失败: " + e.getMessage());
        }
    }

    /** 根据业务数据执行指定版本的规则：规则版本ID/版本号可选，如非空则使用指定版本的规则，如空则使用当前最新版的规则； */
    @PublicInterface(
            id = "3446b3f8-36f2-4288-b7e4-ce62e60a942a",
            module = "rule_engine",
            moduleId = "e03ca7ad-ee08-468d-9c8a-1b7d14950192",
            version = "1748916606529",
            pubRpc = false)
    @AutoGenerated(locked = false, uuid = "3446b3f8-36f2-4288-b7e4-ce62e60a942a")
    public Boolean executeRule(
            @Valid @NotNull List<RuleVersionEo> ruleVersionList, @NotNull String businessData) {
        if (CollectionUtils.isEmpty(ruleVersionList) || !StringUtils.hasText(businessData)) {
            log.warn("规则版本列表为空或业务数据为空，无法执行规则");
            return false;
        }

        try {
            log.info("开始执行规则，规则版本数量: {}", ruleVersionList.size());

            // 获取完整的规则版本信息
            List<RuleVersionBaseDto> completeRuleVersionList =
                    getCompleteRuleVersionList(ruleVersionList);

            if (CollectionUtils.isEmpty(completeRuleVersionList)) {
                log.warn("未找到有效的规则版本信息");
                return false;
            }

            // 获取规则引擎适配器
            RuleEngineAdapter ruleEngineAdapter = ruleEngineAdapterFactory.getDefaultAdapter();

            // 执行规则
            Boolean result = ruleEngineAdapter.executeRule(completeRuleVersionList, businessData);

            log.info("规则执行完成，结果: {}", result);
            return result;

        } catch (Exception e) {
            log.error("规则执行失败", e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "规则执行失败: " + e.getMessage());
        }
    }

    /**
     * 获取完整的规则版本信息
     * 支持多种查询方式，优先级：ruleVersionId > (ruleId + versionNumber) > ruleId > ruleCode
     * 当同时提供ID和code时，如果查询结果不一致，会抛出异常
     *
     * @param ruleVersionList 规则版本EO列表
     * @return 完整的规则版本DTO列表
     */
    private List<RuleVersionBaseDto> getCompleteRuleVersionList(
            List<RuleVersionEo> ruleVersionList) {
        List<RuleVersionBaseDto> result = new ArrayList<>();

        for (RuleVersionEo ruleVersionEo : ruleVersionList) {
            RuleVersionBaseDto ruleVersionDto = findRuleVersionByPriority(ruleVersionEo);

            if (ruleVersionDto != null) {
                result.add(ruleVersionDto);
            } else {
                log.warn(
                        "未找到规则版本信息: ruleId={}, ruleVersionId={}, versionNumber={}, ruleCode={}",
                        ruleVersionEo.getRuleId(),
                        ruleVersionEo.getRuleVersionId(),
                        ruleVersionEo.getVersionNumber(),
                        ruleVersionEo.getRuleCode());
            }
        }

        return result;
    }

    /**
     * 按优先级查找规则版本
     * 优先级：ruleVersionId > (ruleId + versionNumber) > ruleId > ruleCode
     *
     * @param ruleVersionEo 规则版本EO
     * @return 规则版本DTO，未找到返回null
     */
    private RuleVersionBaseDto findRuleVersionByPriority(RuleVersionEo ruleVersionEo) {
        // 1. 优先级最高：根据规则版本ID查询
        if (StringUtils.hasText(ruleVersionEo.getRuleVersionId())) {
            RuleVersionBaseDto ruleVersionDto =
                    ruleVersionBaseDtoManager.getById(ruleVersionEo.getRuleVersionId());
            if (ruleVersionDto != null) {
                log.debug("根据规则版本ID获取规则版本: {}", ruleVersionEo.getRuleVersionId());

                // 验证ID和code的一致性
                validateRuleIdAndCodeConsistency(ruleVersionEo, ruleVersionDto);
                return ruleVersionDto;
            }
        }

        // 2. 第二优先级：根据规则ID和版本号查询
        if (StringUtils.hasText(ruleVersionEo.getRuleId())
                && StringUtils.hasText(ruleVersionEo.getVersionNumber())) {
            RuleVersionBaseDto ruleVersionDto = findByRuleIdAndVersionNumber(ruleVersionEo);
            if (ruleVersionDto != null) {
                // 验证ID和code的一致性
                validateRuleIdAndCodeConsistency(ruleVersionEo, ruleVersionDto);
                return ruleVersionDto;
            }
        }

        // 3. 第三优先级：根据规则ID获取最新版本
        if (StringUtils.hasText(ruleVersionEo.getRuleId())) {
            RuleVersionBaseDto ruleVersionDto = findLatestByRuleId(ruleVersionEo.getRuleId());
            if (ruleVersionDto != null) {
                // 验证ID和code的一致性
                validateRuleIdAndCodeConsistency(ruleVersionEo, ruleVersionDto);
                return ruleVersionDto;
            }
        }

        // 4. 最低优先级：根据规则编码获取最新版本
        if (StringUtils.hasText(ruleVersionEo.getRuleCode())) {
            return findLatestByRuleCode(ruleVersionEo.getRuleCode());
        }

        return null;
    }

    /**
     * 根据规则ID和版本号查询规则版本
     *
     * @param ruleVersionEo 包含规则ID和版本号的EO对象
     * @return 匹配的规则版本DTO，未找到返回null
     */
    private RuleVersionBaseDto findByRuleIdAndVersionNumber(RuleVersionEo ruleVersionEo) {
        RuleVersion.RuleIdAndVersionNumber query = new RuleVersion.RuleIdAndVersionNumber();
        query.setRuleId(ruleVersionEo.getRuleId());
        query.setVersionNumber(ruleVersionEo.getVersionNumber());

        RuleVersionBaseDto ruleVersionDto =
                ruleVersionBaseDtoManager.getByRuleIdAndVersionNumber(query);
        if (ruleVersionDto != null) {
            log.debug(
                    "根据规则ID和版本号获取规则版本: {} - {} -> 规则版本ID: {}",
                    ruleVersionEo.getRuleId(),
                    ruleVersionEo.getVersionNumber(),
                    ruleVersionDto.getId());
        } else {
            log.debug(
                    "根据规则ID和版本号未找到规则版本: {} - {}",
                    ruleVersionEo.getRuleId(),
                    ruleVersionEo.getVersionNumber());
        }
        return ruleVersionDto;
    }

    /**
     * 根据规则ID获取最新版本
     * 注意：此方法依赖于Manager层返回的数据排序，如果排序不准确可能影响结果
     *
     * @param ruleId 规则ID
     * @return 最新的规则版本DTO，未找到返回null
     */
    private RuleVersionBaseDto findLatestByRuleId(String ruleId) {
        List<RuleVersionBaseDto> ruleVersions = ruleVersionBaseDtoManager.getByRuleId(ruleId);
        if (!CollectionUtils.isEmpty(ruleVersions)) {
            // 获取最新版本（依赖Manager层的排序逻辑，通常按创建时间排序，取最后一个）
            RuleVersionBaseDto latestVersion = ruleVersions.get(ruleVersions.size() - 1);
            log.debug("根据规则ID获取最新规则版本: {} -> 版本号: {}",
                    ruleId, latestVersion.getVersionNumber());
            return latestVersion;
        }
        log.debug("根据规则ID未找到任何规则版本: {}", ruleId);
        return null;
    }

    /**
     * 根据规则编码获取最新版本
     * 先通过规则编码查询规则信息，再获取该规则的最新版本
     *
     * @param ruleCode 规则编码
     * @return 最新的规则版本DTO，未找到返回null
     */
    private RuleVersionBaseDto findLatestByRuleCode(String ruleCode) {
        try {
            // 先根据规则编码查询规则信息
            RuleBaseDto ruleDto = ruleBaseDtoManager.getByCode(ruleCode);
            if (ruleDto == null) {
                log.debug("根据规则编码未找到规则: {}", ruleCode);
                return null;
            }

            // 再根据规则ID获取最新版本
            RuleVersionBaseDto latestVersion = findLatestByRuleId(ruleDto.getId());
            if (latestVersion != null) {
                log.debug("根据规则编码获取最新规则版本: {} -> 规则ID: {} -> 版本号: {}",
                        ruleCode, ruleDto.getId(), latestVersion.getVersionNumber());
            } else {
                log.debug("根据规则编码找到规则但无版本信息: {} -> 规则ID: {}", ruleCode, ruleDto.getId());
            }
            return latestVersion;
        } catch (Exception e) {
            log.error("根据规则编码查询规则版本失败: {}", ruleCode, e);
            return null;
        }
    }

    /**
     * 验证规则ID和规则编码的一致性
     * 当同时提供ruleId和ruleCode时，检查它们是否指向同一个规则
     *
     * @param ruleVersionEo 输入的规则版本EO
     * @param foundRuleVersion 查询到的规则版本DTO
     */
    private void validateRuleIdAndCodeConsistency(
            RuleVersionEo ruleVersionEo, RuleVersionBaseDto foundRuleVersion) {
        // 如果没有同时提供ruleId和ruleCode，则无需验证
        if (!StringUtils.hasText(ruleVersionEo.getRuleId())
                || !StringUtils.hasText(ruleVersionEo.getRuleCode())) {
            return;
        }

        try {
            // 根据规则编码查询规则信息
            RuleBaseDto ruleByCode = ruleBaseDtoManager.getByCode(ruleVersionEo.getRuleCode());
            if (ruleByCode == null) {
                log.warn("一致性验证：根据规则编码未找到规则，跳过验证: {}", ruleVersionEo.getRuleCode());
                return;
            }

            // 检查规则ID是否一致
            if (!Objects.equals(ruleVersionEo.getRuleId(), ruleByCode.getId())) {
                String errorMsg = String.format(
                        "规则ID和规则编码不匹配：提供的规则ID=%s，规则编码=%s对应的规则ID=%s，规则名称=%s",
                        ruleVersionEo.getRuleId(),
                        ruleVersionEo.getRuleCode(),
                        ruleByCode.getId(),
                        ruleByCode.getDisplayName());
                log.error(errorMsg);
                throw new IgnoredException(ErrorCode.SYS_ERROR, errorMsg);
            }

            // 进一步检查查询到的规则版本是否属于正确的规则
            if (!Objects.equals(foundRuleVersion.getRuleId(), ruleByCode.getId())) {
                String errorMsg = String.format(
                        "查询到的规则版本不属于指定的规则：规则编码=%s，期望规则ID=%s，实际规则ID=%s，规则版本ID=%s",
                        ruleVersionEo.getRuleCode(),
                        ruleByCode.getId(),
                        foundRuleVersion.getRuleId(),
                        foundRuleVersion.getId());
                log.error(errorMsg);
                throw new IgnoredException(ErrorCode.SYS_ERROR, errorMsg);
            }

            log.debug("规则ID和规则编码一致性验证通过：规则ID={}, 规则编码={}, 规则版本ID={}",
                    ruleVersionEo.getRuleId(), ruleVersionEo.getRuleCode(), foundRuleVersion.getId());

        } catch (IgnoredException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("验证规则ID和规则编码一致性时发生异常：规则ID={}, 规则编码={}",
                    ruleVersionEo.getRuleId(), ruleVersionEo.getRuleCode(), e);
            throw new IgnoredException(ErrorCode.SYS_ERROR, "验证规则ID和规则编码一致性失败: " + e.getMessage());
        }
    }
}
