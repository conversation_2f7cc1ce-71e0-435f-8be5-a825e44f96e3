package com.pulse.application.persist.qto;

import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@AutoGenerated(locked = true, uuid = "24456524-75f4-4bc7-91eb-b347ac389fd0|QTO|DEFINITION")
public class ListRouterWithParentQto {
    /** 启用标识 router.enable_flag */
    @AutoGenerated(locked = true, uuid = "404bee5d-5576-4e08-8b22-49ba7fccadcb")
    private Boolean enableFlagIs;

    @AutoGenerated(locked = true, uuid = "18cb1e98-8748-4372-80e7-d90a2bb4cd05")
    private Integer from;

    /** 主键 router.id */
    @AutoGenerated(locked = true, uuid = "47060993-d374-4c57-9ae4-8fd65d5873d7")
    private String searchLike;

    @AutoGenerated(locked = true, uuid = "5baf6715-ef8e-4bee-81d6-984757c0dea6")
    private Integer size;
}
