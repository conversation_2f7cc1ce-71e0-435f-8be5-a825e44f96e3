package com.pulse.application.service.query;

import com.pulse.application.manager.converter.RouterDtoConverter;
import com.pulse.application.manager.dto.RouterBaseDto;
import com.pulse.application.manager.dto.RouterDto;
import com.pulse.application.persist.qto.ListRouterWithParentQto;
import com.pulse.application.service.RouterBaseDtoService;
import com.pulse.application.service.index.entity.ListRouterWithParentQtoService;
import com.pulse.application.service.query.assembler.RouterDtoDataAssembler;
import com.pulse.application.service.query.assembler.RouterDtoDataAssembler.RouterDtoDataHolder;
import com.pulse.application.service.query.collector.RouterDtoDataCollector;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/** RouterDto查询方案入口 */
@Service
@AutoGenerated(locked = false, uuid = "99101f8f-ce25-30b9-8780-05c0cfc8ef8b")
public class RouterDtoQueryService {
    @AutoGenerated(locked = true)
    @Resource
    private ListRouterWithParentQtoService listRouterWithParentQtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RouterBaseDtoService routerBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RouterDtoConverter routerDtoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private RouterDtoDataAssembler routerDtoDataAssembler;

    @AutoGenerated(locked = true)
    @Resource
    private RouterDtoDataCollector routerDtoDataCollector;

    /** 将ID列表转换为DtoList */
    @AutoGenerated(locked = true, uuid = "85d18d12-857d-351b-a8e2-4a7f04da8373")
    private List<RouterDto> toDtoList(List<String> ids, RouterDtoDataHolder dataHolder) {
        List<RouterBaseDto> baseDtoList = routerBaseDtoService.getByIds(ids);
        dataHolder.setRootBaseDtoList(baseDtoList);
        Map<String, RouterDto> dtoMap =
                routerDtoConverter.convertFromRouterBaseDtoToRouterDto(baseDtoList).stream()
                        .collect(Collectors.toMap(RouterDto::getId, Function.identity()));
        return ids.stream()
                .map(id -> dtoMap.get(id))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /** 根据ListRouterWithParentQto查询RouterDto列表,分页 */
    @PublicInterface(id = "a7abec3e-e61a-4512-b651-88c599b5b99c", module = "application")
    @AutoGenerated(locked = false, uuid = "e02e24e4-e807-33df-a864-1a9a95400598")
    public VSQueryResult<RouterDto> listRouterWithParentPaged(
            @Valid @NotNull ListRouterWithParentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<String> ids = listRouterWithParentQtoService.queryPaged(qto);
        RouterDtoDataHolder dataHolder = new RouterDtoDataHolder();
        List<RouterDto> dtoList = toDtoList(ids, dataHolder);
        routerDtoDataCollector.collectDataDefault(dataHolder);
        routerDtoDataAssembler.assembleData(dtoList, dataHolder);
        VSQueryResult result = new VSQueryResult();
        result.setCount(listRouterWithParentQtoService.count(qto));
        result.setResult(dtoList);
        result.setFrom(qto.getFrom());
        result.setSize(qto.getSize());
        /** This block is generated by vs, do not modify, end anchor 1 */
        // 自定义逻辑,e.g. 数据过滤、重组、批量处理等

        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }
}
