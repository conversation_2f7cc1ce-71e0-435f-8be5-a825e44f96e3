package com.pulse.application.service.query.assembler;

import cn.hutool.core.collection.CollectionUtil;

import com.pulse.application.manager.dto.FeatureBaseDto;
import com.pulse.application.manager.dto.RouterBaseDto;
import com.pulse.application.manager.dto.RouterDto;
import com.pulse.application.service.RouterBaseDtoService;
import com.vs.code.AutoGenerated;

import lombok.Getter;
import lombok.Setter;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** RouterDto数据填充 */
@Component
@AutoGenerated(locked = false, uuid = "6f208d4f-ceeb-398f-804d-9cf6cd5c9a6b")
public class RouterDtoDataAssembler {
    @AutoGenerated(locked = true)
    @Resource
    private RouterBaseDtoService routerBaseDtoService;

    /** 批量自定义组装RouterDto数据，如填充自定义字段等 */
    @AutoGenerated(locked = false, uuid = "1c5b4fe0-128b-3521-9e24-b5c75f149e67")
    public void assembleDataCustomized(List<RouterDto> dataList) {
        // 自定义数据组装

    }

    /** 组装RouterDto数据 */
    @AutoGenerated(locked = true, uuid = "8d30d64c-02f4-3016-a880-e22db8755dad")
    public void assembleData(
            List<RouterDto> dtoList, RouterDtoDataAssembler.RouterDtoDataHolder dataHolder) {
        if (CollectionUtil.isEmpty(dtoList)) {
            return;
        }
        Map<String, RouterBaseDto> baseDtoMap =
                dataHolder.getRootBaseDtoList().stream()
                        .collect(Collectors.toMap(RouterBaseDto::getId, Function.identity()));

        Map<String, FeatureBaseDto> feature =
                dataHolder.feature.stream()
                        .collect(Collectors.toMap(FeatureBaseDto::getId, Function.identity()));

        for (RouterDto dto : dtoList) {
            dto.setFeature(
                    Optional.ofNullable(feature.get(baseDtoMap.get(dto.getId()).getFeatureId()))
                            .orElse(null));
        }

        assembleDataCustomized(dtoList); // 自定义处理逻辑
    }

    /** 持有Dto需要的全部数据 */
    @Getter
    @Setter
    public static class RouterDtoDataHolder {
        /** root base dto数据 */
        @AutoGenerated(locked = true)
        public List<RouterBaseDto> rootBaseDtoList;

        /** 持有dto字段feature的Dto数据 */
        @AutoGenerated(locked = true)
        public List<FeatureBaseDto> feature;
    }
}
