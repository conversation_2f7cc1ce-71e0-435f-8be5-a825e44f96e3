package com.pulse.application.service.query.collector;

import com.pulse.application.manager.dto.FeatureBaseDto;
import com.pulse.application.manager.dto.RouterBaseDto;
import com.pulse.application.service.FeatureBaseDtoService;
import com.pulse.application.service.RouterBaseDtoService;
import com.pulse.application.service.query.assembler.RouterDtoDataAssembler.RouterDtoDataHolder;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/** 获取组装RouterDto所需数据 */
@Component
@AutoGenerated(locked = true, uuid = "133c3335-d7ba-3159-8841-62da4351b298")
public class RouterDtoDataCollector {
    @AutoGenerated(locked = true)
    @Resource
    private FeatureBaseDtoService featureBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RouterBaseDtoService routerBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RouterDtoDataCollector routerDtoDataCollector;

    /** 按需填充数据，同类型数据合并获取 */
    @AutoGenerated(locked = true, uuid = "73e166e7-3457-3778-b223-029326e8d9e3")
    private void fillDataWhenNecessary(RouterDtoDataHolder dataHolder) {
        List<RouterBaseDto> rootDtoList = dataHolder.rootBaseDtoList;
        if (dataHolder.feature == null) {
            Set<String> ids =
                    rootDtoList.stream()
                            .map(RouterBaseDto::getFeatureId)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet());
            List<FeatureBaseDto> baseDtoList =
                    featureBaseDtoService.getByIds(new ArrayList<>(ids)).stream()
                            .sorted(Comparator.comparing(FeatureBaseDto::getId))
                            .collect(Collectors.toList());
            Map<String, FeatureBaseDto> baseDtoMap =
                    baseDtoList.stream()
                            .collect(Collectors.toMap(FeatureBaseDto::getId, Function.identity()));
            dataHolder.feature =
                    rootDtoList.stream()
                            .map(RouterBaseDto::getFeatureId)
                            .distinct()
                            .map(tmpId -> baseDtoMap.get(tmpId))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList());
        }
    }

    /** Qto无Filter, 默认数据全部采集 */
    @AutoGenerated(locked = true, uuid = "d47cb25a-2035-35f0-9c50-d5e73531169b")
    public void collectDataDefault(RouterDtoDataHolder dataHolder) {
        routerDtoDataCollector.fillDataWhenNecessary(dataHolder);
    }
}
