package com.pulse.application.service.index.entity;

import cn.hutool.core.lang.Assert;

import com.pulse.application.persist.mapper.ListRouterWithParentQtoDao;
import com.pulse.application.persist.qto.ListRouterWithParentQto;
import com.vs.code.AutoGenerated;

import org.springframework.stereotype.Component;

import java.util.List;

import javax.annotation.Resource;

@Component
@AutoGenerated(locked = true, uuid = "24456524-75f4-4bc7-91eb-b347ac389fd0|QTO|SERVICE")
public class ListRouterWithParentQtoService {
    @AutoGenerated(locked = true)
    @Resource
    private ListRouterWithParentQtoDao listRouterWithParentMapper;

    /** 分页查询入口 */
    @AutoGenerated(locked = true, uuid = "24456524-75f4-4bc7-91eb-b347ac389fd0-query-paged")
    public List<String> queryPaged(ListRouterWithParentQto qto) {
        Assert.isTrue(qto.getSize() != null && qto.getSize() > 0, "size 字段必须大于0 ");
        Assert.isTrue(qto.getFrom() != null && qto.getFrom() >= 0, "from 字段必须大于等于0 ");
        return listRouterWithParentMapper.queryPaged(qto);
    }

    /** 计算总数量 */
    @AutoGenerated(locked = true)
    public Integer count(ListRouterWithParentQto qto) {
        return listRouterWithParentMapper.count(qto);
    }
}
