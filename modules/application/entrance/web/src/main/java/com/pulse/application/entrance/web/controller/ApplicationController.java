package com.pulse.application.entrance.web.controller;

import com.pulse.application.entrance.web.converter.ApplicationDepartmentBaseVoConverter;
import com.pulse.application.entrance.web.converter.ApplicationDepartmentVoConverter;
import com.pulse.application.entrance.web.converter.ApplicationDetailVoConverter;
import com.pulse.application.entrance.web.converter.ApplicationDetailsBaseVoConverter;
import com.pulse.application.entrance.web.converter.ApplicationInfoOrganizationVoConverter;
import com.pulse.application.entrance.web.converter.ApplicationSimpleVoConverter;
import com.pulse.application.entrance.web.converter.ApplicationVoConverter;
import com.pulse.application.entrance.web.converter.FeatureBaseVoConverter;
import com.pulse.application.entrance.web.converter.FeatureVoConverter;
import com.pulse.application.entrance.web.converter.MenuParentVoConverter;
import com.pulse.application.entrance.web.converter.MenuRouterVoConverter;
import com.pulse.application.entrance.web.converter.MenuVoConverter;
import com.pulse.application.entrance.web.converter.OrganizationWithApplicationVoConverter;
import com.pulse.application.entrance.web.converter.RouterFeatureVoConverter;
import com.pulse.application.entrance.web.converter.RouterInfoBaseVoConverter;
import com.pulse.application.entrance.web.converter.RouterVoConverter;
import com.pulse.application.entrance.web.query.executor.ApplicationInfoOrganizationVoQueryExecutor;
import com.pulse.application.entrance.web.query.executor.MenuAppRouterVoQueryExecutor;
import com.pulse.application.entrance.web.vo.ApplicationDepartmentBaseVo;
import com.pulse.application.entrance.web.vo.ApplicationDepartmentVo;
import com.pulse.application.entrance.web.vo.ApplicationDetailVo;
import com.pulse.application.entrance.web.vo.ApplicationDetailsBaseVo;
import com.pulse.application.entrance.web.vo.ApplicationInfoOrganizationVo;
import com.pulse.application.entrance.web.vo.ApplicationSimpleVo;
import com.pulse.application.entrance.web.vo.ApplicationVo;
import com.pulse.application.entrance.web.vo.FeatureBaseVo;
import com.pulse.application.entrance.web.vo.FeatureVo;
import com.pulse.application.entrance.web.vo.MenuAppRouterVo;
import com.pulse.application.entrance.web.vo.MenuParentVo;
import com.pulse.application.entrance.web.vo.MenuRouterVo;
import com.pulse.application.entrance.web.vo.MenuVo;
import com.pulse.application.entrance.web.vo.OrganizationWithApplicationVo;
import com.pulse.application.entrance.web.vo.RouterFeatureVo;
import com.pulse.application.entrance.web.vo.RouterInfoBaseVo;
import com.pulse.application.entrance.web.vo.RouterVo;
import com.pulse.application.manager.dto.ApplicationBaseDto;
import com.pulse.application.manager.dto.ApplicationDepartmentBaseDto;
import com.pulse.application.manager.dto.ApplicationDepartmentDto;
import com.pulse.application.manager.dto.ApplicationDetailDto;
import com.pulse.application.manager.dto.ApplicationDetailsBaseDto;
import com.pulse.application.manager.dto.ApplicationInfoOrganizationDto;
import com.pulse.application.manager.dto.FeatureBaseDto;
import com.pulse.application.manager.dto.FeatureDto;
import com.pulse.application.manager.dto.MenuBaseDto;
import com.pulse.application.manager.dto.MenuDto;
import com.pulse.application.manager.dto.MenuRouterDto;
import com.pulse.application.manager.dto.OrganizationWithApplicationInfoDto;
import com.pulse.application.manager.dto.RouterBaseDto;
import com.pulse.application.persist.qto.*;
import com.pulse.application.manager.dto.RouterDto;
import com.pulse.application.persist.qto.ListApplicationAllQto;
import com.pulse.application.persist.qto.ListApplicationByParentIdQto;
import com.pulse.application.persist.qto.ListApplicationDetailQto;
import com.pulse.application.persist.qto.ListApplicationOrganizationQto;
import com.pulse.application.persist.qto.ListApplicationQto;
import com.pulse.application.persist.qto.ListApplicationSimpleQto;
import com.pulse.application.persist.qto.ListFeatureBySearchLikeQto;
import com.pulse.application.persist.qto.ListFeatureWithParentQto;
import com.pulse.application.persist.qto.ListMenuByParentIdQto;
import com.pulse.application.persist.qto.ListMenuByRouterIdQto;
import com.pulse.application.persist.qto.ListMenuQto;
import com.pulse.application.persist.qto.ListMenuRouterQto;
import com.pulse.application.persist.qto.ListMenuWithParentQto;
import com.pulse.application.persist.qto.ListOrganizationWithApplicationInfoQto;
import com.pulse.application.persist.qto.ListParentApplicationQto;
import com.pulse.application.persist.qto.ListRouterExternalQto;
import com.pulse.application.persist.qto.ListRouterParentQto;
import com.pulse.application.persist.qto.ListRouterQto;
import com.pulse.application.persist.qto.ListRouterWithParentQto;
import com.pulse.application.persist.qto.SearchApplicationOrganizationByOrganizationIdQto;
import com.pulse.application.service.*;
import com.pulse.application.service.ApplicationBOService;
import com.pulse.application.service.ApplicationDepartmentBaseDtoService;
import com.pulse.application.service.ApplicationDepartmentDtoService;
import com.pulse.application.service.ApplicationDetailDtoService;
import com.pulse.application.service.FeatureBOService;
import com.pulse.application.service.MenuBOService;
import com.pulse.application.service.RouterBOService;
import com.pulse.application.service.RouterBaseDtoService;
import com.pulse.application.service.bto.*;
import com.pulse.application.service.bto.CreateApplicationBto;
import com.pulse.application.service.bto.CreateApplicationOrganizationBto;
import com.pulse.application.service.bto.CreateApplicationOrganizationDetailBto;
import com.pulse.application.service.bto.CreateDepartmentBto;
import com.pulse.application.service.bto.CreateDepartmentsBto;
import com.pulse.application.service.bto.CreateDetailBto;
import com.pulse.application.service.bto.CreateFeatureBto;
import com.pulse.application.service.bto.CreateMenuBto;
import com.pulse.application.service.bto.CreateRouterBto;
import com.pulse.application.service.bto.DeleteApplicationOrganizationBto;
import com.pulse.application.service.bto.DeleteDepartmentBto;
import com.pulse.application.service.bto.DeleteDetailBto;
import com.pulse.application.service.bto.MergeApplicationBto;
import com.pulse.application.service.bto.MergeApplicationDetailBto;
import com.pulse.application.service.bto.MergeApplicationOrganizationBto;
import com.pulse.application.service.bto.MergeDepartmentsBto;
import com.pulse.application.service.bto.MergeDetailBto;
import com.pulse.application.service.bto.MergeFeatureBto;
import com.pulse.application.service.bto.MergeMenuBto;
import com.pulse.application.service.bto.MergeRouterBto;
import com.pulse.application.service.bto.UpdateApplicationStatusBto;
import com.pulse.application.service.bto.UpdateFeatureActiveBto;
import com.pulse.application.service.bto.UpdateListApplicationMenuBto;
import com.pulse.application.service.bto.UpdateMenuActiveBto;
import com.pulse.application.service.bto.UpdateRouterActiveBto;
import com.pulse.application.service.query.*;
import com.pulse.application.service.query.ApplicationBaseDtoQueryService;
import com.pulse.application.service.query.ApplicationDetailsBaseDtoQueryService;
import com.pulse.application.service.query.ApplicationInfoOrganizationDtoQueryService;
import com.pulse.application.service.query.FeatureBaseDtoQueryService;
import com.pulse.application.service.query.FeatureDtoQueryService;
import com.pulse.application.service.query.MenuBaseDtoQueryService;
import com.pulse.application.service.query.MenuDtoQueryService;
import com.pulse.application.service.query.MenuRouterDtoQueryService;
import com.pulse.application.service.query.OrganizationWithApplicationInfoDtoQueryService;
import com.pulse.application.service.query.RouterBaseDtoQueryService;
import com.pulse.application.service.query.RouterDtoQueryService;
import com.vs.code.AutoGenerated;
import com.vs.common.util.rpc.pub.PublicInterface;
import com.vs.es.query.VSQueryResult;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import java.util.List;
import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;

@Controller
@Validated
@AutoGenerated(locked = false, uuid = "f6c76d16-9fb3-3b73-a77a-77f573881b4b")
public class ApplicationController {
    @AutoGenerated(locked = true)
    @Resource
    private ApplicationBOService applicationBOService;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationBaseDtoQueryService applicationBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationDepartmentBaseDtoService applicationDepartmentBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationDepartmentBaseVoConverter applicationDepartmentBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationDepartmentDtoService applicationDepartmentDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationDepartmentVoConverter applicationDepartmentVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationDetailDtoService applicationDetailDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationDetailVoConverter applicationDetailVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationDetailsBaseDtoQueryService applicationDetailsBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationDetailsBaseVoConverter applicationDetailsBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationInfoOrganizationDtoQueryService applicationInfoOrganizationDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationOrganizationDtoQueryService applicationOrganizationDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationInfoOrganizationVoConverter applicationInfoOrganizationVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationInfoOrganizationVoQueryExecutor applicationInfoOrganizationVoQueryExecutor;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationSimpleVoConverter applicationSimpleVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private ApplicationVoConverter applicationVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private FeatureBOService featureBOService;

    @AutoGenerated(locked = true)
    @Resource
    private FeatureBaseDtoQueryService featureBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private FeatureBaseVoConverter featureBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private FeatureDtoQueryService featureDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private FeatureVoConverter featureVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private MenuAppRouterVoQueryExecutor menuAppRouterVoQueryExecutor;

    @AutoGenerated(locked = true)
    @Resource
    private MenuBOService menuBOService;

    @AutoGenerated(locked = true)
    @Resource
    private MenuBaseDtoQueryService menuBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private MenuDtoQueryService menuDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private MenuParentVoConverter menuParentVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private MenuRouterDtoQueryService menuRouterDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private MenuRouterVoConverter menuRouterVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private MenuVoConverter menuVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWithApplicationInfoDtoQueryService
            organizationWithApplicationInfoDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private OrganizationWithApplicationVoConverter organizationWithApplicationVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private RouterBOService routerBOService;

    @AutoGenerated(locked = true)
    @Resource
    ApplicationDepartmentBaseDtoQueryService applicationDepartmentBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private RouterBaseDtoQueryService routerBaseDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private RouterBaseDtoService routerBaseDtoService;

    @AutoGenerated(locked = true)
    @Resource
    private RouterDtoQueryService routerDtoQueryService;

    @AutoGenerated(locked = true)
    @Resource
    private RouterFeatureVoConverter routerFeatureVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private RouterInfoBaseVoConverter routerInfoBaseVoConverter;

    @AutoGenerated(locked = true)
    @Resource
    private RouterVoConverter routerVoConverter;

    /** merge菜单 */
    @PublicInterface(id = "004be538-a564-4fe2-b3c7-52eb20a14375", version = "1744879547270")
    @AutoGenerated(locked = false, uuid = "004be538-a564-4fe2-b3c7-52eb20a14375")
    @RequestMapping(
            value = {"/api/application/merge-menu"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeMenu(@Valid MergeMenuBto mergeMenuBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = menuBOService.mergeMenu(mergeMenuBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 删除应用组织关系及对照 */
    @PublicInterface(id = "00d9776f-dd8a-4318-8cc8-03b9bd8dbc5c", version = "1743647815711")
    @AutoGenerated(locked = false, uuid = "00d9776f-dd8a-4318-8cc8-03b9bd8dbc5c")
    @RequestMapping(
            value = {"/api/application/delete-application-organization"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteApplicationOrganization(
            @Valid
                    DeleteApplicationOrganizationBto.ApplicationOrganizationBto
                            applicationOrganizationBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                applicationBOService.deleteApplicationOrganization(applicationOrganizationBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 路由列表 */
    @PublicInterface(id = "02ef80cb-ed6b-418f-b3fe-74c7d4e5d6b6", version = "1744879644095")
    @AutoGenerated(locked = false, uuid = "02ef80cb-ed6b-418f-b3fe-74c7d4e5d6b6")
    @RequestMapping(
            value = {"/api/application/list-router"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<RouterInfoBaseVo> listRouter(@Valid ListRouterParentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<RouterBaseDto> rpcResult = routerBaseDtoQueryService.listRouterParent(qto);
        List<RouterInfoBaseVo> result =
                routerInfoBaseVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据主键获取应用组织关系列表 */
    @PublicInterface(id = "03008d85-76a8-4d34-993b-220b51717db8", version = "1743585598629")
    @AutoGenerated(locked = false, uuid = "03008d85-76a8-4d34-993b-220b51717db8")
    @RequestMapping(
            value = {"/api/application/get-by-id-list"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<ApplicationDetailVo> getByIdList(@Valid List<String> id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationDetailDto> rpcResult = applicationDetailDtoService.getByIds(id);
        List<ApplicationDetailVo> result =
                applicationDetailVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 更新应用对照 */
    @PublicInterface(id = "041096c8-f729-4c22-b383-5d1167861ea0", version = "1744879512125")
    @AutoGenerated(locked = false, uuid = "041096c8-f729-4c22-b383-5d1167861ea0")
    @RequestMapping(
            value = {"/api/application/merge-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeDetail(@Valid MergeDetailBto.ApplicationDetailBto applicationDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = applicationBOService.mergeDetail(applicationDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 模糊查询功能点列表 */
    @PublicInterface(id = "17738d0c-d2c2-4723-93a2-f4f145f7c161", version = "1744283950322")
    @AutoGenerated(locked = false, uuid = "17738d0c-d2c2-4723-93a2-f4f145f7c161")
    @RequestMapping(
            value = {"/api/application/list-feature-query-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<FeatureBaseVo> listFeatureQueryPaged(
            @Valid ListFeatureBySearchLikeQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<FeatureBaseDto> dtoResult =
                featureBaseDtoQueryService.listFeatureBySearchLikePaged(qto);
        VSQueryResult<FeatureBaseVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(featureBaseVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 删除应用 */
    @PublicInterface(id = "1872998f-683f-4255-b0de-21722b46f7e7", version = "1744007402259")
    @AutoGenerated(locked = false, uuid = "1872998f-683f-4255-b0de-21722b46f7e7")
    @RequestMapping(
            value = {"/api/application/delete-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteDetail(@Valid DeleteDetailBto.ApplicationDetailBto applicationDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = applicationBOService.deleteDetail(applicationDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据主键获取路由 */
    @PublicInterface(id = "1981a856-0433-4c4b-bbfe-af974a0e5fb6", version = "1744878487900")
    @AutoGenerated(locked = false, uuid = "1981a856-0433-4c4b-bbfe-af974a0e5fb6")
    @RequestMapping(
            value = {"/api/application/get-router-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public RouterVo getRouterById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        RouterBaseDto rpcResult = routerBaseDtoService.getById(id);
        RouterVo result = routerVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 获取父应用列表 */
    @PublicInterface(id = "1a253cd9-bcfc-4517-8b49-3296c0e5966f", version = "1744877880743")
    @AutoGenerated(locked = false, uuid = "1a253cd9-bcfc-4517-8b49-3296c0e5966f")
    @RequestMapping(
            value = {"/api/application/list-parent-application"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<ApplicationVo> listParentApplication(@Valid ListParentApplicationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationBaseDto> rpcResult =
                applicationBaseDtoQueryService.listParentApplication(qto);
        List<ApplicationVo> result = applicationVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 通过父菜单id获取菜单列表 */
    @PublicInterface(id = "24568c5f-a6b8-4b0c-a180-ffc41145628f", version = "1743499200737")
    @AutoGenerated(locked = false, uuid = "24568c5f-a6b8-4b0c-a180-ffc41145628f")
    @RequestMapping(
            value = {"/api/application/list-menu-by-parent-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<MenuAppRouterVo> listMenuByParentId(
            @Valid @NotNull(message = "查询参数不能为空") ListMenuByParentIdQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<MenuAppRouterVo> result = menuAppRouterVoQueryExecutor.queryListMenuByParentId(qto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // access response
        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 模糊查询菜单路由 */
    @PublicInterface(id = "25c8a07e-f51b-4b40-80bc-c56bc92b89f5", version = "1744879955179")
    @AutoGenerated(locked = false, uuid = "25c8a07e-f51b-4b40-80bc-c56bc92b89f5")
    @RequestMapping(
            value = {"/api/application/list-menu-router-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<MenuRouterVo> listMenuRouterByQueryPaged(@Valid ListMenuRouterQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<MenuRouterDto> dtoResult = menuRouterDtoQueryService.listMenuRouterPaged(qto);
        VSQueryResult<MenuRouterVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(menuRouterVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 新增应用对照 */
    @PublicInterface(id = "2bb9288c-f21b-40a0-85e3-d98cbb5aa15c", version = "1744878430940")
    @AutoGenerated(locked = false, uuid = "2bb9288c-f21b-40a0-85e3-d98cbb5aa15c")
    @RequestMapping(
            value = {"/api/application/create-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createDetail(@Valid CreateDetailBto.ApplicationDetailBto applicationDetailBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = applicationBOService.createDetail(applicationDetailBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询菜单 */
    @PublicInterface(id = "2fc77f59-06c4-4509-8ecc-7a53b3f012f6", version = "1749008628933")
    @AutoGenerated(locked = false, uuid = "2fc77f59-06c4-4509-8ecc-7a53b3f012f6")
    @RequestMapping(
            value = {"/api/application/list-menu-with-parent-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<MenuParentVo> listMenuWithParentPaged(@Valid ListMenuWithParentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<MenuDto> dtoResult = menuDtoQueryService.listMenuWithParentPaged(qto);
        VSQueryResult<MenuParentVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(menuParentVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 更新科室应用对照 */
    @PublicInterface(id = "33502299-f8c8-4c5a-8185-e1c7102e2e8e", version = "1749029765785")
    @AutoGenerated(locked = false, uuid = "33502299-f8c8-4c5a-8185-e1c7102e2e8e")
    @RequestMapping(
            value = {"/api/application/merge-departments-batch"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeDepartmentsBatch(
            @Valid List<MergeDepartmentsBto.ApplicationDepartmentBto> merge,
            @Valid List<DeleteDepartmentBto.ApplicationDepartmentBto> delete) {
        // TODO implement method
        for (MergeDepartmentsBto.ApplicationDepartmentBto item : merge) {
            String result = applicationBOService.mergeDepartments(item);
        }
        for (DeleteDepartmentBto.ApplicationDepartmentBto item : delete) {
            String result = applicationBOService.deleteDepartment(item);
        }
        return null;
    }

    /** merge应用关系和对照 */
    @PublicInterface(id = "3502486a-2323-40fa-9c89-b9ea7f2a518d", version = "1743646658759")
    @AutoGenerated(locked = false, uuid = "3502486a-2323-40fa-9c89-b9ea7f2a518d")
    @RequestMapping(
            value = {"/api/application/merge-application-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeApplicationDetail(
            @Valid
                    MergeApplicationDetailBto.ApplicationOrganizationBto
                            applicationOrganizationBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = applicationBOService.mergeApplicationDetail(applicationOrganizationBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据组织id查找应用组织关系（带应用对象） */
    @PublicInterface(id = "35e41666-5c4a-44f3-83a6-eabf87f942b1", version = "1743500256153")
    @AutoGenerated(locked = false, uuid = "35e41666-5c4a-44f3-83a6-eabf87f942b1")
    @RequestMapping(
            value = {"/api/application/list-application-organization-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<ApplicationInfoOrganizationVo> listApplicationOrganizationPaged(
            @Valid SearchApplicationOrganizationByOrganizationIdQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<ApplicationInfoOrganizationDto> dtoResult =
                applicationInfoOrganizationDtoQueryService.getApplicationOrganization(qto);
        VSQueryResult<ApplicationInfoOrganizationVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                applicationInfoOrganizationVoConverter.convertAndAssembleDataList(
                        dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据组织id查找应用组织关系（带应用对象） */
    @PublicInterface(id = "36b16ca4-9b2c-4eb5-802f-fb83c61e9f68", version = "1743500010400")
    @AutoGenerated(locked = false, uuid = "36b16ca4-9b2c-4eb5-802f-fb83c61e9f68")
    @RequestMapping(
            value = {"/api/application/list-application-organization-query"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<ApplicationInfoOrganizationVo> listApplicationOrganizationQuery(
            @Valid SearchApplicationOrganizationByOrganizationIdQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationInfoOrganizationDto> rpcResult =
                applicationInfoOrganizationDtoQueryService.searchApplicationByOrganizationId(qto);
        List<ApplicationInfoOrganizationVo> result =
                applicationInfoOrganizationVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 新增应用组织关系 */
    @PublicInterface(id = "38213fdb-b23d-47d0-9937-9fbe332c0ea8", version = "1743587892145")
    @AutoGenerated(locked = false, uuid = "38213fdb-b23d-47d0-9937-9fbe332c0ea8")
    @RequestMapping(
            value = {"/api/application/create-application-organization"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createApplicationOrganization(
            @Valid
                    CreateApplicationOrganizationBto.ApplicationOrganizationBto
                            applicationOrganizationBto) {
        // 判断是否重复
        ListApplicationOrganizationSearchQto qto = new ListApplicationOrganizationSearchQto();
        qto.setApplicationIdIs(applicationOrganizationBto.getApplicationId());
        qto.setOrganizationIdIs(applicationOrganizationBto.getOrganizationId());
        var retQto = applicationOrganizationDtoQueryService.listApplicationOrganizationSearch(qto);
        if (!(retQto.size() > 0)) {
            return "新增失败!应用:"
                    + applicationOrganizationBto.getApplicationId()
                    + "组织:"
                    + applicationOrganizationBto.getOrganizationId()
                    + "已存在";
        } else {
            /** This block is generated by vs, do not modify, start anchor 1 */
            String result =
                    applicationBOService.createApplicationOrganization(applicationOrganizationBto);
            /** This block is generated by vs, do not modify, end anchor 1 */
            return result;
        }
    }

    /** 获取外部系统路由 */
    @PublicInterface(id = "3ff8e3a4-2b7a-4072-b606-43748649ebdc", version = "1743580142277")
    @AutoGenerated(locked = false, uuid = "3ff8e3a4-2b7a-4072-b606-43748649ebdc")
    @RequestMapping(
            value = {"/api/application/list-router-external-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<RouterVo> listRouterExternalPaged(@Valid ListRouterExternalQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<RouterBaseDto> dtoResult =
                routerBaseDtoQueryService.listRouterExternalPaged(qto);
        VSQueryResult<RouterVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(routerVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询所有应用 */
    @PublicInterface(id = "467c908e-8401-48e0-8e62-b8de9c696e1f", version = "1744769837147")
    @AutoGenerated(locked = false, uuid = "467c908e-8401-48e0-8e62-b8de9c696e1f")
    @RequestMapping(
            value = {"/api/application/list-application-all"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<ApplicationVo> listApplicationAll(@Valid ListApplicationAllQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationBaseDto> rpcResult = applicationBaseDtoQueryService.listApplicationAll(qto);
        List<ApplicationVo> result = applicationVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 新增功能 新增功能记录 */
    @PublicInterface(id = "468b4185-6925-454e-afb5-04a9c3a68c75", version = "1744878414775")
    @AutoGenerated(locked = false, uuid = "468b4185-6925-454e-afb5-04a9c3a68c75")
    @RequestMapping(
            value = {"/api/application/create-feature"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createFeature(@Valid CreateFeatureBto createFeatureBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = featureBOService.createFeature(createFeatureBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据应用获取应用_科室列表 */
    @PublicInterface(id = "46995bd9-764d-4317-bf59-25a2bffe2b47", version = "1744715745611")
    @AutoGenerated(locked = false, uuid = "46995bd9-764d-4317-bf59-25a2bffe2b47")
    @RequestMapping(
            value = {"/api/application/department-by-application-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<ApplicationDepartmentBaseVo> departmentByApplicationId(String applicationId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationDepartmentBaseDto> rpcResult =
                applicationDepartmentBaseDtoService.getByApplicationId(applicationId);
        List<ApplicationDepartmentBaseVo> result =
                applicationDepartmentBaseVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 修改菜单启用标志 */
    @PublicInterface(id = "49014ea1-7e92-45ab-8a81-03c4c542dee1", version = "1744879565374")
    @AutoGenerated(locked = false, uuid = "49014ea1-7e92-45ab-8a81-03c4c542dee1")
    @RequestMapping(
            value = {"/api/application/update-menu-active"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateMenuActive(@Valid UpdateMenuActiveBto updateMenuActiveBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = menuBOService.updateMenuActive(updateMenuActiveBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 获取应用列表 */
    @PublicInterface(id = "4df467fd-30a8-47e2-9580-63261297b984", version = "1744879631660")
    @AutoGenerated(locked = false, uuid = "4df467fd-30a8-47e2-9580-63261297b984")
    @RequestMapping(
            value = {"/api/application/list-application-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<ApplicationVo> listApplicationPaged(@Valid ListApplicationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<ApplicationBaseDto> dtoResult =
                applicationBaseDtoQueryService.listApplicationPaged(qto);
        VSQueryResult<ApplicationVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(applicationVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 查询菜单 模糊查询菜单，通过searchLike */
    @PublicInterface(id = "6033397f-8446-4bdc-bd92-50cde24bd6db", version = "1743498792694")
    @AutoGenerated(locked = false, uuid = "6033397f-8446-4bdc-bd92-50cde24bd6db")
    @RequestMapping(
            value = {"/api/application/list-menu-by-query-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<MenuVo> listMenuByQueryPaged(@Valid ListMenuQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<MenuBaseDto> dtoResult = menuBaseDtoQueryService.listMenuPaged(qto);
        VSQueryResult<MenuVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(menuVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据父应用ID取子应用列表 */
    @PublicInterface(id = "6b1e9813-842c-40ff-9ade-4aca167616a5", version = "1743558354932")
    @AutoGenerated(locked = false, uuid = "6b1e9813-842c-40ff-9ade-4aca167616a5")
    @RequestMapping(
            value = {"/api/application/list-application-by-parent-id-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<ApplicationVo> listApplicationByParentIdPaged(
            @Valid ListApplicationByParentIdQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<ApplicationBaseDto> dtoResult =
                applicationBaseDtoQueryService.queryListApplicationByParentIdPaged(qto);
        VSQueryResult<ApplicationVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(applicationVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 路由列表(含功能名称) */
    @PublicInterface(id = "6bb2bb02-847b-4b7e-a563-cc0b2da5d55e", version = "1749089397597")
    @AutoGenerated(locked = false, uuid = "6bb2bb02-847b-4b7e-a563-cc0b2da5d55e")
    @RequestMapping(
            value = {"/api/application/list-router-with-parent-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<RouterFeatureVo> listRouterWithParentPaged(
            @Valid ListRouterWithParentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<RouterDto> dtoResult = routerDtoQueryService.listRouterWithParentPaged(qto);
        VSQueryResult<RouterFeatureVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                routerFeatureVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 修改功能启用标识 */
    @PublicInterface(id = "6cde7b95-6bf6-4125-9ef6-590f833709ee", version = "1742970962296")
    @AutoGenerated(locked = false, uuid = "6cde7b95-6bf6-4125-9ef6-590f833709ee")
    @RequestMapping(
            value = {"/api/application/update-feature-active"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateFeatureActive(@Valid UpdateFeatureActiveBto updateFeatureActiveBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = featureBOService.updateFeatureActive(updateFeatureActiveBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 获取应用对照列表 */
    @PublicInterface(id = "6e73d91d-fff1-4d34-8f58-b246e34236f3", version = "1743585359523")
    @AutoGenerated(locked = false, uuid = "6e73d91d-fff1-4d34-8f58-b246e34236f3")
    @RequestMapping(
            value = {"/api/application/list-application-detail-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<ApplicationDetailsBaseVo> listApplicationDetailPaged(
            @Valid ListApplicationDetailQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<ApplicationDetailsBaseDto> dtoResult =
                applicationDetailsBaseDtoQueryService.listApplicationDetailPaged(qto);
        VSQueryResult<ApplicationDetailsBaseVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                applicationDetailsBaseVoConverter.convertAndAssembleDataList(
                        dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 新增应用关系详细 新增应用关系记录 +新增对应的应用对照详细记录 */
    @PublicInterface(id = "795b1f6e-262a-4a17-8629-d4a1f79e0ff2", version = "1747882589043")
    @AutoGenerated(locked = false, uuid = "795b1f6e-262a-4a17-8629-d4a1f79e0ff2")
    @RequestMapping(
            value = {"/api/application/create-application-organization-detail"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createApplicationOrganizationDetail(
            @Valid
                    CreateApplicationOrganizationDetailBto.ApplicationOrganizationBto
                            applicationOrganizationBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                applicationBOService.createApplicationOrganizationDetail(
                        applicationOrganizationBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 创建应用 */
    @PublicInterface(id = "7e9369e8-a4ad-428b-bb2b-02a784451750", version = "1744878440217")
    @AutoGenerated(locked = false, uuid = "7e9369e8-a4ad-428b-bb2b-02a784451750")
    @RequestMapping(
            value = {"/api/application/create-departments"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createDepartments(@Valid CreateDepartmentsBto createDepartmentsBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = applicationBOService.createDepartments(createDepartmentsBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 新增菜单 */
    @PublicInterface(id = "82d5959a-04f3-4743-b7e4-1e3d2d8ae8e3", version = "1744878403520")
    @AutoGenerated(locked = false, uuid = "82d5959a-04f3-4743-b7e4-1e3d2d8ae8e3")
    @RequestMapping(
            value = {"/api/application/create-menu"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createMenu(@Valid CreateMenuBto createMenuBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = menuBOService.createMenu(createMenuBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 批量新增路由 */
    @PublicInterface(id = "830ea009-5830-47ac-9a7c-8b0ec90ba0ad", version = "1744877906406")
    @AutoGenerated(locked = false, uuid = "830ea009-5830-47ac-9a7c-8b0ec90ba0ad")
    @RequestMapping(
            value = {"/api/application/create-router-batch"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createRouterBatch(@Valid List<CreateRouterBto> routers) {
        // TODO implement method
        String retmsg = "";
        RouterByPageUrlQto qto = new RouterByPageUrlQto();
        for (CreateRouterBto Router : routers) {
            if (!(Router.getFrontPageUrl() == null)) {
                qto.setFrontPageUrlIs(Router.getFrontPageUrl());
                var resultRouter = routerBaseDtoQueryService.routerByPageUrl(qto);
                if (!(resultRouter.size() > 0)) {
                    String result = routerBOService.createRouter(Router);
                } else {
                    retmsg += "pageUrl=" + Router.getFrontPageUrl() + "的路由已存在！";
                }
            } else {
                String result = routerBOService.createRouter(Router);
            }
        }
        return retmsg;
    }

    /** 获取应用列表 */
    @PublicInterface(id = "8b001913-3099-42f4-ab06-37ea6731e39b", version = "1744878695283")
    @AutoGenerated(locked = false, uuid = "8b001913-3099-42f4-ab06-37ea6731e39b")
    @RequestMapping(
            value = {"/api/application/list-application-simple-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<ApplicationSimpleVo> listApplicationSimplePaged(
            @Valid ListApplicationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<ApplicationBaseDto> dtoResult =
                applicationBaseDtoQueryService.listApplicationPaged(qto);
        VSQueryResult<ApplicationSimpleVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(
                applicationSimpleVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 模糊查询功能点含父功能点 */
    @PublicInterface(id = "91600745-1afa-46ad-ba68-79d51e8af125", version = "1747310367937")
    @AutoGenerated(locked = false, uuid = "91600745-1afa-46ad-ba68-79d51e8af125")
    @RequestMapping(
            value = {"/api/application/list-feature-with-parent-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<FeatureVo> listFeatureWithParentPaged(
            @Valid ListFeatureWithParentQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<FeatureDto> dtoResult =
                featureDtoQueryService.listFeatureWithParentPaged(qto);
        VSQueryResult<FeatureVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(featureVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 获取组织应用关系对照 */
    @PublicInterface(id = "9644d286-dbd8-4341-9f21-631c117219c8", version = "1743499301736")
    @AutoGenerated(locked = false, uuid = "9644d286-dbd8-4341-9f21-631c117219c8")
    @RequestMapping(
            value = {"/api/application/list-application-organization"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<ApplicationInfoOrganizationVo> listApplicationOrganization(
            @Valid @NotNull(message = "查询参数不能为空") ListApplicationOrganizationQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationInfoOrganizationVo> result =
                applicationInfoOrganizationVoQueryExecutor.queryByListApplicationOrganization(qto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // access response
        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 创建应用科室对照 */
    @PublicInterface(id = "9d5dcc3f-9e82-4b74-af65-c3afd5e3fb6e", version = "1744263821360")
    @AutoGenerated(locked = false, uuid = "9d5dcc3f-9e82-4b74-af65-c3afd5e3fb6e")
    @RequestMapping(
            value = {"/api/application/create-department"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createDepartment(
            @Valid CreateDepartmentBto.ApplicationDepartmentBto applicationDepartmentBto) {
        String result = "";
        GetDepartmentQto qto = new GetDepartmentQto();
        qto.setApplicationIdIs(applicationDepartmentBto.getApplicationId());
        qto.setDepartmentIdIs(applicationDepartmentBto.getDepartmentId());
        var depRet = applicationDepartmentBaseDtoQueryService.getDepartment(qto);
        if (depRet.size() > 0) {
        } else {
            /** This block is generated by vs, do not modify, start anchor 1 */
            result = applicationBOService.createDepartment(applicationDepartmentBto);
            /** This block is generated by vs, do not modify, end anchor 1 */
        }
        return result;
    }

    /** 修改应用状态 */
    @PublicInterface(id = "b16cad19-0ba6-4c88-8702-76e6f686f2e0", version = "1744879604435")
    @AutoGenerated(locked = false, uuid = "b16cad19-0ba6-4c88-8702-76e6f686f2e0")
    @RequestMapping(
            value = {"/api/application/update-application-status"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateApplicationStatus(
            @Valid UpdateApplicationStatusBto updateApplicationStatusBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = applicationBOService.updateApplicationStatus(updateApplicationStatusBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 条件：应用ID,组织ID */
    @PublicInterface(id = "b1eadd3d-785a-4bb2-b57b-7207e399dc99", version = "1749019945602")
    @AutoGenerated(locked = false, uuid = "b1eadd3d-785a-4bb2-b57b-7207e399dc99")
    @RequestMapping(
            value = {"/api/application/delete-department-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteDepartmentById(
            @Valid DeleteDepartmentBto.ApplicationDepartmentBto applicationDepartmentBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = applicationBOService.deleteDepartment(applicationDepartmentBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 通过路由id获取菜单列表 */
    @PublicInterface(id = "bcbbf894-b11c-43d0-b0e3-4b66d691b185", version = "1745390413003")
    @AutoGenerated(locked = false, uuid = "bcbbf894-b11c-43d0-b0e3-4b66d691b185")
    @RequestMapping(
            value = {"/api/application/query-list-menu-by-router-id-waterfall"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<MenuAppRouterVo> queryListMenuByRouterIdWaterfall(
            @Valid @NotNull(message = "查询参数不能为空") ListMenuByRouterIdQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<MenuAppRouterVo> result =
                menuAppRouterVoQueryExecutor.queryListMenuByRouterIdWaterfall(qto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        // access response
        /** This block is generated by vs, do not modify, start anchor 2 */
        return result;
        /** This block is generated by vs, do not modify, end anchor 2 */
    }

    /** 查询菜单 查询菜单列表 */
    @PublicInterface(id = "c2329787-e0be-42ca-abd0-baa70c3c24d1", version = "1743573230287")
    @AutoGenerated(locked = false, uuid = "c2329787-e0be-42ca-abd0-baa70c3c24d1")
    @RequestMapping(
            value = {"/api/application/list-menu-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<MenuVo> listMenuPaged(@Valid ListMenuQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<MenuBaseDto> dtoResult = menuBaseDtoQueryService.listMenuPaged(qto);
        VSQueryResult<MenuVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(menuVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 获取组织应用关系 获取院区组织应用关系 */
    @PublicInterface(id = "c416aa45-6d7d-4cdf-a787-570aa94acce7", version = "1743499428241")
    @AutoGenerated(locked = false, uuid = "c416aa45-6d7d-4cdf-a787-570aa94acce7")
    @RequestMapping(
            value = {"/api/application/list-organization-with-application-info"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<OrganizationWithApplicationVo> listOrganizationWithApplicationInfo(
            @Valid ListOrganizationWithApplicationInfoQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<OrganizationWithApplicationInfoDto> rpcResult =
                organizationWithApplicationInfoDtoQueryService.listOrganizationWithApplicationInfo(
                        qto);
        List<OrganizationWithApplicationVo> result =
                organizationWithApplicationVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据科室ID获取应用_科室列表 */
    @PublicInterface(id = "c702cd6d-c14b-490c-b68d-8b0faf5053e5", version = "1745477454100")
    @AutoGenerated(locked = false, uuid = "c702cd6d-c14b-490c-b68d-8b0faf5053e5")
    @RequestMapping(
            value = {"/api/application/get-by-department-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public List<ApplicationDepartmentVo> getByDepartmentId(String departmentId) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        List<ApplicationDepartmentDto> rpcResult =
                applicationDepartmentDtoService.getByDepartmentId(departmentId);
        List<ApplicationDepartmentVo> result =
                applicationDepartmentVoConverter.convertAndAssembleDataList(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 新增应用 新增应用记录 */
    @PublicInterface(id = "c755d8d4-3185-4875-abe0-d81ef6a33bc3", version = "1744877843854")
    @AutoGenerated(locked = false, uuid = "c755d8d4-3185-4875-abe0-d81ef6a33bc3")
    @RequestMapping(
            value = {"/api/application/create-application"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createApplication(@Valid CreateApplicationBto createApplicationBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = applicationBOService.createApplication(createApplicationBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 修改应用菜单 */
    @PublicInterface(id = "c92c4176-8ea3-457c-bde3-31d840538c7f", version = "1743560233108")
    @AutoGenerated(locked = false, uuid = "c92c4176-8ea3-457c-bde3-31d840538c7f")
    @RequestMapping(
            value = {"/api/application/update-list-application-menu"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateListApplicationMenu(
            @Valid UpdateListApplicationMenuBto.ApplicationMenuBto applicationMenuBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = applicationBOService.updateListApplicationMenu(applicationMenuBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** merge应用 更新应用 */
    @PublicInterface(id = "d09c627b-3372-4c3c-8e43-c5782c8c6fb3", version = "1742902077655")
    @AutoGenerated(locked = false, uuid = "d09c627b-3372-4c3c-8e43-c5782c8c6fb3")
    @RequestMapping(
            value = {"/api/application/merge-application"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeApplication(@Valid MergeApplicationBto mergeApplicationBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = applicationBOService.mergeApplication(mergeApplicationBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** merge路由 */
    @PublicInterface(id = "d127c91f-1a3a-4add-af0d-fde92878768f", version = "1742902423339")
    @AutoGenerated(locked = false, uuid = "d127c91f-1a3a-4add-af0d-fde92878768f")
    @RequestMapping(
            value = {"/api/application/merge-router"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeRouter(@Valid MergeRouterBto mergeRouterBto) {
        RouterByPageUrlQto qto = new RouterByPageUrlQto();
        String result = "";
        if (!(mergeRouterBto.getFrontPageUrl() == null)) {
            qto.setFrontPageUrlIs(mergeRouterBto.getFrontPageUrl());
            var resultRouter = routerBaseDtoQueryService.routerByPageUrl(qto);
            if (((resultRouter.size() > 0)
                            && resultRouter.get(0).getId().equals(mergeRouterBto.getId()))
                    || resultRouter.size() == 0) {
                result = routerBOService.mergeRouter(mergeRouterBto);
            } else {
                result += "pageUrl=" + mergeRouterBto.getFrontPageUrl() + "的路由已存在！";
            }
        } else {
            /** This block is generated by vs, do not modify, start anchor 1 */
            result = routerBOService.mergeRouter(mergeRouterBto);
            /** This block is generated by vs, do not modify, end anchor 1 */
        }
        return result;
    }

    /** 应用下拉框绑定 */
    @PublicInterface(id = "de4c3a6f-1163-4b34-ae09-c2b127e972cd", version = "1744787008122")
    @AutoGenerated(locked = false, uuid = "de4c3a6f-1163-4b34-ae09-c2b127e972cd")
    @RequestMapping(
            value = {"/api/application/list-application-waterfall"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<ApplicationVo> listApplicationWaterfall(
            @Valid ListApplicationSimpleQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<ApplicationBaseDto> dtoResult =
                applicationBaseDtoQueryService.listApplicationWaterfall(qto);
        VSQueryResult<ApplicationVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(applicationVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 根据主键获取应用组织关系 */
    @PublicInterface(id = "e51afd30-26d2-47df-a56f-a03f8aed67ea", version = "1744879977007")
    @AutoGenerated(locked = false, uuid = "e51afd30-26d2-47df-a56f-a03f8aed67ea")
    @RequestMapping(
            value = {"/api/application/get-by-id"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public ApplicationDetailVo getById(String id) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        ApplicationDetailDto rpcResult = applicationDetailDtoService.getById(id);
        ApplicationDetailVo result = applicationDetailVoConverter.convertAndAssembleData(rpcResult);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** merge应用组织关系 */
    @PublicInterface(id = "e67e1b2f-9424-4eab-b8ab-25709229afcf", version = "1743476605940")
    @AutoGenerated(locked = false, uuid = "e67e1b2f-9424-4eab-b8ab-25709229afcf")
    @RequestMapping(
            value = {"/api/application/merge-application-organization"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeApplicationOrganization(
            @Valid MergeApplicationOrganizationBto mergeApplicationOrganizationBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result =
                applicationBOService.mergeApplicationOrganization(mergeApplicationOrganizationBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 新增路由 */
    @PublicInterface(id = "e7e1a2be-dca7-4aa7-8529-b2d2e376824d", version = "1744878391455")
    @AutoGenerated(locked = false, uuid = "e7e1a2be-dca7-4aa7-8529-b2d2e376824d")
    @RequestMapping(
            value = {"/api/application/create-router"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String createRouter(@Valid CreateRouterBto createRouterBto) {
        RouterByPageUrlQto qto = new RouterByPageUrlQto();
        String result = "";
        if (!(createRouterBto.getFrontPageUrl() == null)) {
            qto.setFrontPageUrlIs(createRouterBto.getFrontPageUrl());
            var resultRouter = routerBaseDtoQueryService.routerByPageUrl(qto);
            if (!(resultRouter.size() > 0)) {
                result = routerBOService.createRouter(createRouterBto);
            } else {
                result += "pageUrl=" + createRouterBto.getFrontPageUrl() + "的路由已存在！";
            }
        } else {
            /** This block is generated by vs, do not modify, start anchor 1 */
            result = routerBOService.createRouter(createRouterBto);
            /** This block is generated by vs, do not modify, end anchor 1 */
        }

        return result;
    }

    /** 修改路由弃用标志 */
    @PublicInterface(id = "e7f15d5a-ee42-49cc-a0b5-556d1409cdf9", version = "1744879575750")
    @AutoGenerated(locked = false, uuid = "e7f15d5a-ee42-49cc-a0b5-556d1409cdf9")
    @RequestMapping(
            value = {"/api/application/update-router-active"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String updateRouterActive(@Valid UpdateRouterActiveBto updateRouterActiveBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = routerBOService.updateRouterActive(updateRouterActiveBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 按组织ID和应用ID删除 */
    @PublicInterface(id = "f537e7d8-cf24-49d8-81ac-4974735ff3c3", version = "1744709869933")
    @AutoGenerated(locked = false, uuid = "f537e7d8-cf24-49d8-81ac-4974735ff3c3")
    @RequestMapping(
            value = {"/api/application/delete-department"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String deleteDepartment(String applicationId, String groupId) {
        // TODO implement method
        GetDepartmentQto qto = new GetDepartmentQto();
        qto.setApplicationIdIs(applicationId);
        qto.setDepartmentIdIs(groupId);
        var depRet = applicationDepartmentBaseDtoQueryService.getDepartment(qto);
        if (depRet.size() > 0) {
            DeleteDepartmentBto.ApplicationDepartmentBto bto =
                    new DeleteDepartmentBto.ApplicationDepartmentBto();
            bto.setId(depRet.get(0).getId());
            applicationBOService.deleteDepartment(bto);
        }
        return null;
    }

    /** merge功能 merge功能记录 */
    @PublicInterface(id = "f7b519f1-94c4-4675-8910-9fd3b1999fac", version = "1744879532981")
    @AutoGenerated(locked = false, uuid = "f7b519f1-94c4-4675-8910-9fd3b1999fac")
    @RequestMapping(
            value = {"/api/application/merge-feature"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public String mergeFeature(@Valid MergeFeatureBto mergeFeatureBto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        String result = featureBOService.mergeFeature(mergeFeatureBto);
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }

    /** 路由列表 */
    @PublicInterface(id = "fa633a73-a0c9-463b-a9a0-7298e582bfc6", version = "1743989603707")
    @AutoGenerated(locked = false, uuid = "fa633a73-a0c9-463b-a9a0-7298e582bfc6")
    @RequestMapping(
            value = {"/api/application/list-router-paged"},
            method = {RequestMethod.POST},
            produces = {"application/json;charset=UTF-8"})
    public VSQueryResult<RouterVo> listRouterPaged(@Valid ListRouterQto qto) {
        /** This block is generated by vs, do not modify, start anchor 1 */
        VSQueryResult<RouterBaseDto> dtoResult = routerBaseDtoQueryService.listRouterPaged(qto);
        VSQueryResult<RouterVo> result = new VSQueryResult();
        result.setCount(dtoResult.getCount());
        result.setSize(dtoResult.getSize());
        result.setFrom(dtoResult.getFrom());
        result.setScrollId(dtoResult.getScrollId());
        result.setHasMore(dtoResult.isHasMore());
        result.setResult(routerVoConverter.convertAndAssembleDataList(dtoResult.getResult()));
        /** This block is generated by vs, do not modify, end anchor 1 */
        return result;
    }
}
